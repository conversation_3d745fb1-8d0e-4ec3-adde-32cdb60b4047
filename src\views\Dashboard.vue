<template>
  <div class="dashboard-page">
    <div class="dashboard-content">
      <!-- 流量使用率提醒 -->
      <div v-if="showTrafficAlert" class="traffic-usage-alert">
        <div class="alert-content">
          <div class="alert-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
              <line x1="9" y1="9" x2="9.01" y2="9"/>
              <line x1="15" y1="9" x2="15.01" y2="9"/>
            </svg>
          </div>
          <div class="alert-info">
            <div class="alert-title">流量使用提醒</div>
            <div class="alert-details">
              <span class="traffic-usage">当前已使用流量达 {{ trafficPercentage }}%</span>
              <span v-if="subscribeInfo?.plan?.reset_price !== null && subscribeInfo?.plan?.reset_price !== undefined" class="reset-link" @click="openResetTrafficModal">
                <strong>重置已用流量</strong>
              </span>
            </div>
          </div>
          <div class="alert-actions">
            <n-button quaternary size="small" @click="dismissTrafficAlert">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </n-button>
          </div>
        </div>
      </div>

      <!-- 未支付订单提醒 -->
      <div v-if="unpaidOrder" class="unpaid-order-alert">
        <div class="alert-content">
          <div class="alert-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
          </div>
          <div class="alert-info">
            <div class="alert-title">您有一笔未支付的订单</div>
            <div class="alert-details">
              <span class="order-plan">{{ unpaidOrder.plan?.name || '套餐订单' }}</span>
              <span class="order-period">{{ getPeriodText(unpaidOrder.period) }}</span>
              <span class="order-amount">¥{{ formatMoney(unpaidOrder.total_amount) }}</span>
            </div>
          </div>
          <div class="alert-actions">
            <n-button type="primary" size="small" @click="goToPayment">
              立即支付
            </n-button>
            <n-button quaternary size="small" @click="dismissAlert">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </n-button>
          </div>
        </div>
      </div>

      <!-- 全局加载状态 -->
      <div v-if="loading" class="loading-state">
        <n-spin size="large">
          <template #description>正在加载主页数据...</template>
        </n-spin>
      </div>

      <!-- 主要布局 -->
      <div v-else class="main-layout">
        <!-- 左列 -->
        <div class="left-column">
          <!-- 用户信息大卡片 -->
          <div class="user-card-container">
            <div class="user-main-info">
            <div class="user-profile">
              <div class="user-details">
                <div class="user-email-container">
                  <div class="user-greeting">
                    <span class="greeting-text">欢迎回来，</span>
                  </div>
                  <div class="user-email-wrapper" @click="copyEmail">
                    <div class="user-email-main">{{ formatEmailDisplay(authStore.user?.email || '用户') }}</div>
                    <div class="user-email-domain" v-if="authStore.user?.email">{{ getEmailDomain(authStore.user.email) }}</div>
                    <div class="copy-hint">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                      </svg>
                    </div>
                  </div>
                  <div class="user-badges">
                    <div class="user-status-badge verified">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/>
                        <polyline points="22,4 12,14.01 9,11.01"/>
                      </svg>
                      已验证
                    </div>
                    <div class="user-status-badge member" v-if="subscribeInfo?.plan">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                      会员
                    </div>
                  </div>
                </div>

                <!-- 订阅信息 -->
                <div class="subscription-items">
                  <div class="user-subscription-item">
                    <span class="subscription-label">我的订阅</span>
                    <div class="subscription-value-container">
                      <!-- 重置流量按钮 -->
                      <button
                        v-if="showResetTrafficButton"
                        class="reset-traffic-btn"
                        @click="openResetTrafficModal"
                        :disabled="resettingTraffic"
                      >
                        <div v-if="resettingTraffic" class="loading-spinner-small"></div>
                        {{ resettingTraffic ? '重置中...' : `重置流量 ¥${formatMoney(subscribeInfo?.plan?.reset_price)}` }}
                      </button>
                      <span class="subscription-value">{{ subscribeInfo?.plan?.name || '无订阅' }}</span>
                    </div>
                  </div>
                  <div class="user-subscription-item">
                    <span class="subscription-label">流量重置时间</span>
                    <span class="subscription-value">{{ subscribeInfo?.reset_day ? `每月${subscribeInfo.reset_day}日重置` : '不重置' }}</span>
                  </div>
                  <div class="user-subscription-item">
                    <span class="subscription-label">到期时间</span>
                    <span class="subscription-value">{{ formatExpireTime(authStore.user?.expired_at) }}</span>
                  </div>
                  <!-- 自动续费开关 -->
                  <div class="user-subscription-item auto-renewal-item" v-if="subscribeInfo?.plan">
                    <span class="subscription-label">自动续费</span>
                    <div class="auto-renewal-switch">
                      <n-switch
                        v-model:value="autoRenewalEnabled"
                        :checked-value="1"
                        :unchecked-value="0"
                        size="small"
                        :loading="savingAutoRenewal"
                        @update:value="handleAutoRenewalChange"
                      />
                    </div>
                  </div>
                </div>

                <n-button v-if="!subscribeInfo?.plan" type="primary" @click="$router.push('/plans')" class="buy-button">
                  立即购买订阅
                </n-button>
              </div>
            </div>

            <!-- 右侧：余额卡片组 -->
            <div class="balance-cards-grid-layout">
              <div class="balance-card-new commission-theme">
                <div class="card-glow"></div>
                <div class="card-content-premium">
                  <div class="card-header-premium">
                    <div class="card-icon-premium">
                      <svg width="24" height="24" viewBox="0 0 1024 1024" fill="none">
                        <path d="M637.269333 611.584l-7.355733 173.636267-62.122667-2.679467 6.690134-163.618133-85.486934 10.018133-6.673066 168.2944-62.7712-1.9968 19.3536-502.8864 32.068266-30.037333h283.818667l31.402667 30.037333 16.008533 474.1632-42.734933 30.037333-96.836267-36.7104 22.698667-57.429333 51.438933 20.0192-8.021333-253.098667-85.486934 9.352534-3.328 69.461333 72.123734-8.704 7.338666 62.788267-82.141866 9.352533zM348.740267 384.512h18.688v411.392h-62.7712V443.2896l-32.7168 42.734933L221.866667 447.965867 380.808533 238.933333l50.090667 37.393067-82.141867 108.202667z m148.258133 46.08l86.152533-8.669867 3.9936-96.836266h-86.135466l-4.010667 105.506133z m152.917333-105.506133l-3.9936 90.146133 80.810667-8.021333-2.013867-82.1248h-74.786133zM491.690667 564.8384l85.469866-10.018133 3.345067-69.461334-86.152533 9.352534-2.6624 70.126933z" fill="currentColor"/>
                      </svg>
                    </div>
                    <div class="card-meta">
                      <span class="card-type">Commission</span>
                      <div class="card-indicator"></div>
                    </div>
                  </div>
                  <div class="card-main-content">
                    <div class="card-label-premium">佣金余额</div>
                    <div class="card-amount">¥{{ formatMoney(authStore.user?.commission_balance) }}</div>
                  </div>
                </div>
              </div>

              <div class="balance-card-new account-theme">
                <div class="card-glow"></div>
                <div class="card-content-premium">
                  <div class="card-header-premium">
                    <div class="card-icon-premium">
                      <svg width="24" height="24" viewBox="0 0 1024 1024" fill="none">
                        <path d="M512.******** 16.65706667c-271.******** 0-491.52 220.********-491.52 491.52s220.******** 491.52 491.52 491.52 491.52-220.******** 491.52-491.52-220.********-491.52-491.52-491.52z m154.******** 475.68213333v49.69813333h-121.78773334v68.8128h121.78773334v50.7904h-121.78773334v99.9424H482.5088v-99.9424H355.80586667v-50.7904h126.1568v-68.8128H355.80586667v-49.69813333h107.58826666l-131.072-225.55306667H402.77333333c61.******** 111.******** 98.******** 183.5008 111.******** 213.53813334h1.09226666c4.********-12.******** 16.********-37.******** 36.********-74.82026667l75.3664-138.71786667h66.62826666l-132.7104 226.0992h105.40373334z" fill="currentColor"/>
                      </svg>
                    </div>
                    <div class="card-meta">
                      <span class="card-type">Account</span>
                      <div class="card-indicator"></div>
                    </div>
                  </div>
                  <div class="card-main-content">
                    <div class="card-label-premium">账户余额</div>
                    <div class="card-amount">¥{{ formatMoney(authStore.user?.balance) }}</div>
                  </div>
                </div>
              </div>

              <div class="balance-card-new wallet-theme">
                <div class="card-glow"></div>
                <div class="card-content-premium">
                  <div class="card-header-premium">
                    <div class="card-icon-premium">
                      <svg width="24" height="24" viewBox="0 0 1024 1024" fill="none">
                        <path d="M832 256H192c-35.3 0-64 28.7-64 64v384c0 35.3 28.7 64 64 64h640c35.3 0 64-28.7 64-64V320c0-35.3-28.7-64-64-64z" fill="currentColor" opacity="0.15"/>
                        <path d="M832 192H192c-70.7 0-128 57.3-128 128v384c0 70.7 57.3 128 128 128h640c70.7 0 128-57.3 128-128V320c0-70.7-57.3-128-128-128zM192 256h640c35.3 0 64 28.7 64 64v64H128v-64c0-35.3 28.7-64 64-64zM832 768H192c-35.3 0-64-28.7-64-64V448h768v256c0 35.3-28.7 64-64 64z" fill="currentColor"/>
                        <circle cx="768" cy="576" r="32" fill="currentColor"/>
                        <path d="M256 576h256v32H256z" fill="currentColor" opacity="0.6"/>
                        <path d="M256 640h192v24H256z" fill="currentColor" opacity="0.4"/>
                      </svg>
                    </div>
                    <div class="card-meta">
                      <span class="card-type">Total</span>
                      <div class="card-indicator"></div>
                    </div>
                  </div>
                  <div class="card-main-content">
                    <div class="card-label-premium">我的钱包</div>
                    <div class="card-amount">¥{{ formatMoney((authStore.user?.balance || 0) + (authStore.user?.commission_balance || 0)) }}</div>
                    <button
                      class="ios-card-action"
                      @click="showRechargeModal = true"
                    >
                      充值
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 左下部分 -->
        <div class="left-bottom">
          <!-- 客户端平台选择 -->
          <div class="clients-section">
            <div class="section-title">
              <svg width="20" height="20" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg" class="title-icon">
                <path d="M443.733333 85.333333 102.4 85.333333C83.5584 85.333333 68.266667 100.625067 68.266667 119.466667l0 341.333333c0 18.8416 15.291733 34.133333 34.133333 34.133333l341.333333 0c18.8416 0 34.133333-15.291733 34.133333-34.133333L477.866667 119.466667C477.866667 100.625067 462.574933 85.333333 443.733333 85.333333zM443.733333 546.133333 102.4 546.133333c-18.8416 0-34.133333 15.291733-34.133333 34.133333l0 341.333333c0 18.8416 15.291733 34.133333 34.133333 34.133333l341.333333 0c18.8416 0 34.133333-15.291733 34.133333-34.133333L477.866667 580.266667C477.866667 561.425067 462.574933 546.133333 443.733333 546.133333zM980.974933 268.970667 771.6352 59.613867c-11.5712-11.5712-30.293333-11.5712-41.864533 0L520.413867 268.970667c-11.5712 11.5712-11.5712 30.293333 0 41.864533L729.770667 520.174933c11.5712 11.5712 30.293333 11.5712 41.864533 0l209.3568-209.3568C992.546133 299.264 992.546133 280.541867 980.974933 268.970667zM915.2512 304.913067l-149.538133 149.538133c-8.2432 8.260267-21.6576 8.260267-29.9008 0l-149.538133-149.538133c-8.260267-8.2432-8.260267-21.6576 0-29.9008l149.538133-149.538133c8.2432-8.260267 21.6576-8.260267 29.9008 0l149.538133 149.538133C923.511467 283.272533 923.511467 296.669867 915.2512 304.913067zM921.6 546.133333 580.266667 546.133333c-18.8416 0-34.133333 15.291733-34.133333 34.133333l0 341.333333c0 18.8416 15.291733 34.133333 34.133333 34.133333l341.333333 0c18.8416 0 34.133333-15.291733 34.133333-34.133333L955.733333 580.266667C955.733333 561.425067 940.4416 546.133333 921.6 546.133333z" fill="#91c767"/>
              </svg>
              客户端下载
            </div>
            <div class="platform-cards">
              <!-- Windows 平台容器 -->
              <div class="platform-container">
                <div class="platform-card windows" :class="{ active: selectedPlatform === 'windows', 'mobile-expanded': isMobilePlatformExpanded('windows') }" @click="handlePlatformClick('windows')">
                  <div class="platform-content">
                    <div class="platform-icon">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 12V6.75L9 5.43V11.91L3 12ZM20 3V11.75L10 11.9V5.21L20 3ZM3 13L9 13.09V19.9L3 18.75V13ZM20 13.25V22L10 20.09V13.1L20 13.25Z"/>
                      </svg>
                    </div>
                    <div class="platform-info">
                      <div class="platform-name">Windows</div>
                      <div class="platform-desc">桌面端</div>
                    </div>
                  </div>
                  <!-- 移动端箭头指示器 -->
                  <div class="mobile-platform-arrow" :class="{ rotated: isMobilePlatformExpanded('windows') }">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
                <!-- Windows 客户端展开列表 -->
                <div class="mobile-client-apps" v-if="isMobilePlatformExpanded('windows') && getPlatformApps('windows').filter(app => app.name && app.name.trim()).length > 0">
                  <div class="app-grid">
                    <div
                      v-for="app in getPlatformApps('windows').filter(app => app.name && app.name.trim())"
                      :key="app.key"
                      @click="openClientAppHandler(app)"
                      class="app-item"
                    >
                      <div class="app-icon-wrapper">
                        <img
                          :src="app.img"
                          :alt="app.name"
                          class="app-icon"
                          @contextmenu.prevent
                          @dragstart.prevent
                          @selectstart.prevent
                          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"
                        />
                      </div>
                      <div class="app-name">{{ app.name }}</div>
                      <div class="app-version">{{ app.vs }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Android 平台容器 -->
              <div class="platform-container">
                <div class="platform-card android" :class="{ active: selectedPlatform === 'android', 'mobile-expanded': isMobilePlatformExpanded('android') }" @click="handlePlatformClick('android')">
                  <div class="platform-content">
                    <div class="platform-icon">
                      <svg width="48" height="48" viewBox="0 0 1024 1024" fill="currentColor">
                        <path d="M811.696102 320.103594a62.908461 62.908461 0 0 0-62.204501 63.29244v246.386141c0 34.558056 27.646445 63.228443 62.204501 63.228443a62.844465 62.844465 0 0 0 62.204501-63.228443V383.396034c-0.575968-35.134024-28.222412-63.29244-62.204501-63.29244z m-749.461843 0a62.844465 62.844465 0 0 0-62.204501 63.356436v246.386141c0 34.558056 27.582448 63.228443 62.204501 63.228444a62.332494 62.332494 0 0 0 62.140505-63.228444v-246.386141c0-35.19802-27.582448-63.356436-62.140505-63.356436z m0 0M161.940651 321.895493v450.406665c0 26.878488 21.630783 48.701261 48.637264 48.701261h55.100901v137.208282c0 34.558056 27.582448 63.356436 62.204501 63.356436a62.908461 62.908461 0 0 0 62.204501-63.356436v-137.208282h96.69856v137.208282c0 34.558056 27.582448 63.356436 62.204501 63.356436a62.908461 62.908461 0 0 0 62.204501-63.356436v-137.208282h55.100901a48.637264 48.637264 0 0 0 48.70126-48.701261V321.895493H161.940651z m0 0M567.741825 88.436625L618.746956 14.58478c2.943834-4.671737 2.30387-9.983438-1.727903-12.927273-4.159766-2.943834-9.471467-1.151935-12.99127 2.943834l-52.733033 76.79568c-35.19802-13.439244-73.915842-21.69478-114.937535-21.694779s-79.739515 7.615572-114.937535 21.694779l-52.861026-76.79568a8.959496 8.959496 0 0 0-12.863277-2.943834c-4.09577 2.943834-4.671737 8.831503-1.727903 12.927273l51.645095 73.851845c-80.955446 38.141855-137.848246 109.753826-144.88785 193.525115h552.480923c-7.1036-83.771288-63.868407-155.38326-145.463817-193.525115zM320.267745 205.742027c-17.023042 0-30.462286-13.50324-30.462287-30.526283s13.439244-30.462286 30.462287-30.462286c16.959046 0 30.526283 13.439244 30.526283 30.462286a30.718272 30.718272 0 0 1-30.526283 30.526283z m236.914674 0c-17.087039 0-30.526283-13.50324-30.526283-30.526283s13.439244-30.462286 30.526283-30.462286 30.590279 13.439244 30.590279 30.462286a30.718272 30.718272 0 0 1-30.590279 30.526283z m0 0"/>
                      </svg>
                    </div>
                    <div class="platform-info">
                      <div class="platform-name">Android</div>
                      <div class="platform-desc">安卓手机</div>
                    </div>
                  </div>
                  <!-- 移动端箭头指示器 -->
                  <div class="mobile-platform-arrow" :class="{ rotated: isMobilePlatformExpanded('android') }">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
                <!-- Android 客户端展开列表 -->
                <div class="mobile-client-apps" v-if="isMobilePlatformExpanded('android') && getPlatformApps('android').filter(app => app.name && app.name.trim()).length > 0">
                  <div class="app-grid">
                    <div
                      v-for="app in getPlatformApps('android').filter(app => app.name && app.name.trim())"
                      :key="app.key"
                      @click="openClientAppHandler(app)"
                      class="app-item"
                    >
                      <div class="app-icon-wrapper">
                        <img
                          :src="app.img"
                          :alt="app.name"
                          class="app-icon"
                          @contextmenu.prevent
                          @dragstart.prevent
                          @selectstart.prevent
                          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"
                        />
                      </div>
                      <div class="app-name">{{ app.name }}</div>
                      <div class="app-version">{{ app.vs }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- iOS 平台容器 -->
              <div class="platform-container">
                <div class="platform-card ios" :class="{ active: selectedPlatform === 'ios', 'mobile-expanded': isMobilePlatformExpanded('ios') }" @click="handlePlatformClick('ios')">
                  <div class="platform-content">
                    <div class="platform-icon">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.12997 6.91 8.81997 6.88C10.1 6.86 11.32 7.75 12.11 7.75C12.89 7.75 14.37 6.68 15.92 6.84C16.57 6.87 18.39 7.1 19.56 8.82C19.47 8.88 17.39 10.1 17.41 12.63C17.44 15.65 20.06 16.66 20.09 16.67C20.06 16.74 19.67 18.11 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z"/>
                      </svg>
                    </div>
                    <div class="platform-info">
                      <div class="platform-name">iOS</div>
                      <div class="platform-desc">苹果设备</div>
                    </div>
                  </div>
                  <!-- 移动端箭头指示器 -->
                  <div class="mobile-platform-arrow" :class="{ rotated: isMobilePlatformExpanded('ios') }">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
                <!-- iOS 客户端展开列表 -->
                <div class="mobile-client-apps" v-if="isMobilePlatformExpanded('ios') && getPlatformApps('ios').filter(app => app.name && app.name.trim()).length > 0">
                  <div class="app-grid">
                    <div
                      v-for="app in getPlatformApps('ios').filter(app => app.name && app.name.trim())"
                      :key="app.key"
                      @click="openClientAppHandler(app)"
                      class="app-item"
                    >
                      <div class="app-icon-wrapper">
                        <img
                          :src="app.img"
                          :alt="app.name"
                          class="app-icon"
                          @contextmenu.prevent
                          @dragstart.prevent
                          @selectstart.prevent
                          style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"
                        />
                      </div>
                      <div class="app-name">{{ app.name }}</div>
                      <div class="app-version">{{ app.vs }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>



            <!-- 对应平台的客户端 - 只有当有可用客户端时才显示 -->
            <div class="client-apps" v-if="selectedPlatform && getPlatformApps(selectedPlatform).filter(app => app.name && app.name.trim()).length > 0" :style="{ background: currentTheme.background }">
              <div class="apps-title">
                <span v-html="getPlatformIconSvg(selectedPlatform)" class="title-icon"></span>
                {{ getPlatformTitle(selectedPlatform) }}
              </div>
              <div class="app-grid">
                <div
                  v-for="app in getPlatformApps(selectedPlatform).filter(app => app.name && app.name.trim())"
                  :key="app.key"
                  @click="openClientAppHandler(app)"
                  class="app-item"
                >
                  <div class="app-icon-wrapper">
                    <img
                      :src="app.img"
                      :alt="app.name"
                      class="app-icon"
                      @contextmenu.prevent
                      @dragstart.prevent
                      @selectstart.prevent
                      style="user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none;"
                    />
                  </div>
                  <div class="app-name">{{ app.name }}</div>
                  <div class="app-version">{{ app.vs }}</div>
                </div>
              </div>
            </div>
          </div>


        </div>
        </div>

        <!-- 右列 -->
        <div class="right-column">
          <!-- 重要通知 -->
          <div class="notice-section">
            <div class="section-title">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="title-icon">
                <path d="M14 9V5a3 3 0 0 0-6 0v4"/>
                <rect x="2" y="9" width="20" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="15" r="1"/>
              </svg>
              重要通知
            </div>
            <div class="notice-content" v-if="!noticesLoading">
              <div v-if="notices.length === 0" class="no-notices">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="no-notices-icon">
                  <circle cx="12" cy="12" r="10"/>
                  <path d="M8 12h8"/>
                </svg>
                <div class="no-notices-text">暂无通知</div>
              </div>
              <div v-else class="notices-list">
                <div
                  v-for="notice in notices"
                  :key="notice.id"
                  class="notice-item"
                  :class="{ 'has-image': notice.img_url }"
                  @click="handleNoticeClick(notice)"
                >
                  <div class="notice-content-wrapper">
                    <!-- 通知图片 -->
                    <div v-if="notice.img_url" class="notice-image">
                      <img
                        :src="notice.img_url"
                        :alt="notice.title"
                        @error="handleImageError"
                        @load="handleImageLoad"
                        class="notice-img"
                      />
                    </div>

                    <!-- 通知文字内容 -->
                    <div class="notice-text-content">
                      <div class="notice-header">
                        <div class="notice-title">{{ notice.title }}</div>
                        <div class="notice-time">{{ formatNoticeTime(notice.created_at) }}</div>
                      </div>
                      <div class="notice-text" v-html="truncateContent(notice.content, notice.img_url ? 40 : 60)" @click="handleNoticeContentClick"></div>
                      <div v-if="notice.tags && notice.tags.length > 0" class="notice-tags">
                        <span v-for="tag in notice.tags" :key="tag" class="notice-tag">{{ tag }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="notice-loading">
              <div class="loading-spinner"></div>
              <div class="loading-text">加载中...</div>
            </div>
          </div>

          <!-- 流量仪表盘 -->
          <div class="traffic-dashboard" v-if="authStore.user">
            <div class="section-title">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="title-icon">
                <path d="M3 3v18h18"/>
                <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"/>
              </svg>
              流量使用情况
            </div>
            <!-- 流量仪表盘容器（包含仪表盘和统计卡片） -->
            <div class="traffic-gauge-container">
              <!-- 仪表盘图表部分 -->
              <div class="gauge-visual">
                <div class="gauge-container">
                  <svg width="240" height="160" viewBox="0 0 240 160">
                    <!-- 渐变定义 -->
                    <defs>
                      <!-- 背景渐变 -->
                      <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
                        <stop offset="50%" style="stop-color:#f1f5f9;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
                      </linearGradient>
                      <!-- 动态进度渐变 - 沿弧形方向的线性渐变 -->
                      <linearGradient id="progressGradient" x1="0%" y1="100%" x2="100%" y2="0%" gradientUnits="objectBoundingBox">
                        <stop offset="0%" :style="`stop-color:${trafficColor};stop-opacity:1`" />
                        <stop offset="30%" :style="`stop-color:${trafficColor};stop-opacity:0.9`" />
                        <stop offset="70%" :style="`stop-color:${trafficColor};stop-opacity:0.8`" />
                        <stop offset="100%" :style="`stop-color:${trafficColor};stop-opacity:0.6`" />
                      </linearGradient>
                      <!-- 增强发光效果 -->
                      <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                        <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
                        <feMerge>
                          <feMergeNode in="coloredBlur"/>
                          <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                      </filter>
                      <!-- 内阴影效果 -->
                      <filter id="innerShadow">
                        <feOffset dx="0" dy="2"/>
                        <feGaussianBlur stdDeviation="2" result="offset-blur"/>
                        <feFlood flood-color="#000000" flood-opacity="0.1"/>
                        <feComposite in2="offset-blur" operator="in"/>
                        <feMerge>
                          <feMergeNode/>
                          <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                      </filter>
                    </defs>

                    <!-- 外圈装饰环 -->
                    <circle
                      cx="120"
                      cy="105"
                      r="100"
                      stroke="rgba(148, 163, 184, 0.2)"
                      stroke-width="1"
                      fill="none"
                      stroke-dasharray="2,4"
                    />

                    <!-- 背景弧线 - 更粗更立体 -->
                    <path
                      d="M 25 105 A 95 95 0 0 1 215 105"
                      stroke="url(#bgGradient)"
                      stroke-width="16"
                      fill="none"
                      stroke-linecap="round"
                      filter="url(#innerShadow)"
                    />

                    <!-- 进度弧线 - 使用分段圆弧确保正确显示 -->
                    <g v-for="(segment, index) in getProgressSegments(trafficPercentage)" :key="index">
                      <path
                        :d="segment.path"
                        :stroke="segment.color"
                        stroke-width="16"
                        fill="none"
                        stroke-linecap="round"
                        :opacity="segment.opacity"
                        style="transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1)"
                      />
                    </g>


                  </svg>
                  <div class="gauge-center">
                    <div class="gauge-percentage" :style="{ color: trafficColor }">{{ Math.round(trafficPercentage) }}%</div>
                    <div class="gauge-label">已使用流量</div>
                    <div class="gauge-sublabel">{{ formatTrafficShort(usedTraffic) }} / {{ formatTrafficShort(totalTraffic) }}</div>
                    <div class="gauge-status" :class="trafficStatusClass">
                      <div class="status-dot"></div>
                      {{ trafficStatusText }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 流量统计卡片（在仪表盘容器内） -->
              <div class="traffic-stats-cards">
                <div class="stat-item total-traffic">
                  <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"/>
                      <polyline points="3.27,6.96 12,12.01 20.73,6.96"/>
                      <line x1="12" y1="22.08" x2="12" y2="12"/>
                    </svg>
                  </div>
                  <div class="stat-content">
                    <div class="stat-label">总流量</div>
                    <div class="stat-value">{{ formatTrafficShort(totalTraffic) }}</div>
                  </div>
                </div>
                <div class="stat-item used-traffic">
                  <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M3 3v18h18"/>
                      <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3"/>
                    </svg>
                  </div>
                  <div class="stat-content">
                    <div class="stat-label">已用流量</div>
                    <div class="stat-value">{{ formatTrafficShort(usedTraffic) }}</div>
                  </div>
                </div>
                <div class="stat-item remaining-traffic">
                  <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                      <polyline points="7,10 12,15 17,10"/>
                      <line x1="12" y1="15" x2="12" y2="3"/>
                    </svg>
                  </div>
                  <div class="stat-content">
                    <div class="stat-label">剩余流量</div>
                    <div class="stat-value">{{ formatTrafficShort(remainingTraffic) }}</div>
                  </div>
                </div>
                <div class="stat-item expire-time">
                  <div class="stat-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                      <circle cx="12" cy="12" r="10"/>
                      <polyline points="12,6 12,12 16,14"/>
                    </svg>
                  </div>
                  <div class="stat-content">
                    <div class="stat-label">到期时间</div>
                    <div class="stat-value">{{ formatExpireDays(authStore.user.expired_at) }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 订阅链接容器 -->
          <div class="subscription-container" v-if="subscribeInfo?.subscribe_url">
            <!-- 订阅链接标题（移动端可点击展开） -->
            <div class="subscription-header" @click="toggleSubscriptionExpanded">
              <div class="section-title">
                <svg width="20" height="20" viewBox="0 0 1000 1000" fill="none" xmlns="http://www.w3.org/2000/svg" class="title-icon">
                  <path d="M951.4799 920.1581l0.1989-19.8945c0-472.4627-383.0224-855.469-855.5049-855.469l-19.8953 0.1989v139.06271547890532c406.5584 0 736.1318 329.5656 736.1318 736.1009H951.479858809797zM632.9544 920.1581l0.3967-19.8945c0-296.6659-240.************-537.1774-537.1548l-19.8953 0.3967v138.86484963194954c230.7492 0 417.8053 187.0472 417.8053 417.7877H632.9544409755396zM76.2795 800.7881c0 65.9293 53.************ 119.3721 119.3701 65.9321 0 119.3721-53.4408 119.3721-119.3701 0-65.9203-53.44-119.3641-119.3721-119.3641C129.7225 681.423 76.2795 734.8678 76.2795 800.7881z" fill="#88C152"/>
                </svg>
                订阅链接
              </div>
              <!-- 移动端箭头指示器 -->
              <div class="mobile-subscription-arrow" :class="{ rotated: isSubscriptionExpanded }">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <polyline points="6,9 12,15 18,9"></polyline>
                </svg>
              </div>
            </div>
            <!-- 订阅链接卡片区域 - 全部显示 -->
            <div class="subscription-cards-section" :class="{ 'mobile-expanded': isSubscriptionExpanded }">
              <!-- 所有客户端直接显示 -->
              <div class="subscription-list-all">
                <div class="subscription-item-simple" @click="openSubscriptionModal('general')">
                  <div class="subscription-icon-simple general">
                    <svg width="24" height="24" viewBox="0 0 1024 1024" fill="currentColor">
                      <path d="M512 0c282.760705 0 512 229.239295 512 512s-229.239295 512-512 512S0 794.760705 0 512 229.239295 0 512 0z m-178.297229 770.256927c44.171285 0 79.959698-35.788413 79.959698-79.959698s-35.788413-79.959698-79.959698-79.959698-79.959698 35.788413-79.959698 79.959698 35.788413 79.959698 79.959698 79.959698z m271.798488-31.919396c0-176.685139-143.153652-319.838791-319.83879-319.83879-17.732997 0-31.919395 14.186398-31.919396 31.919395s14.186398 31.919395 31.919396 31.919395c141.219144 0 256 114.458438 256 256 0 17.732997 14.186398 31.919395 31.919395 31.919396s31.919395-14.186398 31.919395-31.919396z m159.919396 0c0-265.027708-214.730479-479.758186-479.758186-479.758186-17.732997 0-31.919395 14.186398-31.919396 31.919396s14.186398 31.919395 31.919396 31.919395c229.561713 0 415.919395 186.035264 415.919395 415.919395 0 17.732997 14.186398 31.919395 31.919395 31.919396s31.919395-14.186398 31.919396-31.919396z" fill="#83CC52"/>
                    </svg>
                  </div>
                  <span class="subscription-name-simple">通用订阅</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('clash-verge')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/clash-verge.png" alt="Clash Verge" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Clash Verge</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('shadowrocket')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/shadowrocket.png" alt="Shadowrocket" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Shadowrocket</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('v2ray')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/v2ray.png" alt="V2Ray" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">V2RayN</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('clash-meta')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/clash-meta.png" alt="ClashMeta" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">ClashMeta</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('flclash')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/flclash.png" alt="FlClash" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">FlClash</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('clash-nyanpasu')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/clash-nyanpasu.png" alt="Clash Nyanpasu" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Clash Nyanpasu</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('stash')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/stash.png" alt="Stash" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Stash</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('singbox')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/singbox.png" alt="SingBox" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">SingBox</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('hiddify')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/hiddify.png" alt="Hiddify" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Hiddify</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('quantumult')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/quantumult.png" alt="Quantumult X" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">QuantumultX</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('surge')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/surge.png" alt="Surge" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Surge</span>
                </div>

                <div class="subscription-item-simple" @click="openSubscriptionModal('loon')">
                  <div class="subscription-icon-simple">
                    <img src="/icons/clients/loon.png" alt="Loon" class="subscription-logo-simple" />
                  </div>
                  <span class="subscription-name-simple">Loon</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 订阅链接模态框 -->
  <SubscriptionDialog
    v-model:show="showSubscriptionModal"
    :title="modalTitle"
    :subtitle="modalSubtitle"
    :subscription-url="currentSubscriptionUrl"
    :supported-clients="getSupportedClients(currentSubscriptionType)"
    :mode="modalMode"
    :manual-instructions="manualInstructions"
    :client-icon="getClientIcon(currentSubscriptionType)"
    @copy="handleCopySubscription"
    @import="handleImportToClient"
  />

  <!-- 通知详情模态框 -->
  <NoticeDetailModal
    v-model:show="showNoticeModal"
    :notice="selectedNotice"
    @close="selectedNotice = null"
  />

  <!-- 充值模态框 -->
  <DesktopModal
    v-if="isRechargeEnabled"
    v-model:show="showRechargeModal"
    title="余额充值"
    :show-close-button="true"
    width="480px"
  >

    <div class="recharge-content">
      <div class="current-balance">
        <span class="label">当前余额：</span>
        <span class="amount">¥{{ formatMoney(authStore.user?.balance) }}</span>
      </div>

      <div class="recharge-form">
        <div class="form-item">
          <label>充值金额</label>
          <n-input-number
            v-model:value="rechargeAmount"
            :min="1"
            :max="100000"
            :step="1"
            placeholder="请输入充值金额"
            style="width: 100%"
          >
            <template #prefix>¥</template>
          </n-input-number>
        </div>

        <div class="quick-amounts">
          <span class="quick-label">快捷金额：</span>
          <div class="amount-buttons">
            <button
              v-for="amount in quickAmounts"
              :key="amount"
              class="btn-base btn-medium btn-secondary amount-btn"
              :class="{ active: rechargeAmount === amount }"
              @click="rechargeAmount = amount"
            >
              ¥{{ amount }}
            </button>
          </div>
        </div>

        <div class="recharge-info">
          <div class="info-item">
            <span>充值后余额：</span>
            <span class="highlight">¥{{ formatMoney((authStore.user?.balance || 0) + (rechargeAmount || 0) * 100) }}</span>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <button class="btn-base btn-large btn-danger" @click="showRechargeModal = false">
          取消
        </button>
        <button
          class="btn-base btn-large btn-primary"
          :disabled="!rechargeAmount || rechargeAmount <= 0 || recharging"
          @click="handleRecharge"
        >
          <span v-if="recharging">充值中...</span>
          <span v-else>确认充值</span>
        </button>
      </div>
    </template>
  </DesktopModal>

  <!-- 支付方式选择模态框 -->
  <DesktopModal
    v-if="isRechargeEnabled"
    v-model:show="showPaymentMethodModal"
    title="选择支付方式"
    :show-close-button="true"
    width="480px"
  >

    <div class="payment-content">
      <div class="payment-info">
        <span class="label">充值金额：</span>
        <span class="amount">¥{{ formatMoney(paymentAmount) }}</span>
      </div>

      <div class="payment-methods-list">
        <div v-if="paymentMethods.length === 0" class="no-payment-methods">
          <p>暂无可用的支付方式</p>
        </div>
        <div
          v-for="method in paymentMethods"
          :key="method.id"
          class="payment-method-item"
          @click="selectPaymentMethod(method)"
        >
          <div class="method-icon">
            <svg v-if="method.name.includes('支付宝')" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="2" y="4" width="20" height="16" rx="2" fill="#1677FF"/>
              <path d="M8 12h8M8 8h8M8 16h4" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <svg v-else-if="method.name.includes('微信')" width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="2" y="4" width="20" height="16" rx="2" fill="#07C160"/>
              <path d="M8 12h8M8 8h8M8 16h4" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
            <svg v-else width="24" height="24" viewBox="0 0 24 24" fill="none">
              <rect x="2" y="4" width="20" height="16" rx="2" fill="#6B7280"/>
              <path d="M8 12h8M8 8h8M8 16h4" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
            </svg>
          </div>
          <div class="method-info">
            <div class="method-name">{{ method.name }}</div>
            <div class="method-desc">{{ method.description || '安全便捷的支付方式' }}</div>
          </div>
          <div class="method-arrow">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9,18 15,12 9,6"/>
            </svg>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <button class="btn-base btn-medium btn-secondary" @click="showPaymentMethodModal = false">
          取消
        </button>
      </div>
    </template>
  </DesktopModal>

  <!-- 重置流量确认模态框 -->
  <DesktopModal
    :show="showResetTrafficModal"
    title="重置流量确认"
    @close="showResetTrafficModal = false"
    :width="'400px'"
  >
    <div class="reset-traffic-modal-content">
      <div class="reset-info">
        <div class="reset-info-item">
          <span class="label">当前套餐：</span>
          <span class="value">{{ subscribeInfo?.plan?.name }}</span>
        </div>
        <div class="reset-info-item">
          <span class="label">重置价格：</span>
          <span class="value price">¥{{ formatMoney(subscribeInfo?.plan?.reset_price) }}</span>
        </div>
        <div class="reset-info-item">
          <span class="label">重置效果：</span>
          <span class="value">已用流量清零，总配额不变</span>
        </div>
      </div>

      <div class="reset-warning">
        <div class="warning-icon">⚠️</div>
        <div class="warning-text">
          <p><strong>重要提醒：</strong></p>
          <p>• 重置后您的已用流量将清零</p>
          <p>• 总流量配额保持不变</p>
          <p>• 此操作不可撤销</p>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-footer-buttons">
        <button
          class="btn-base btn-medium btn-secondary"
          @click="showResetTrafficModal = false"
          :disabled="resettingTraffic"
        >
          取消
        </button>
        <button
          class="btn-base btn-medium btn-warning"
          @click="confirmResetTraffic"
          :disabled="resettingTraffic"
        >
          <div v-if="resettingTraffic" class="loading-spinner-small"></div>
          {{ resettingTraffic ? '处理中...' : '确认重置' }}
        </button>
      </div>
    </template>
  </DesktopModal>

  <!-- 二维码支付模态框 -->
  <DesktopModal
    v-if="isRechargeEnabled"
    v-model:show="showPaymentModal"
    title="扫码支付"
    :show-close-button="true"
    width="480px"
  >
    <div style="padding: 20px 0; text-align: center;">
      <!-- 支付信息 -->
      <div style="display: flex; justify-content: space-between; align-items: center; padding: 16px; background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%); border-radius: 12px; margin-bottom: 24px;">
        <div style="display: flex; flex-direction: column; gap: 4px;">
          <span style="font-size: 12px; color: #64748b; font-weight: 500;">充值金额</span>
          <span style="font-size: 18px; font-weight: 700; color: #3b82f6;">¥{{ formatMoney(paymentAmount) }}</span>
        </div>
        <div style="display: flex; flex-direction: column; gap: 4px;">
          <span style="font-size: 12px; color: #64748b; font-weight: 500;">订单号</span>
          <span style="font-size: 14px; font-weight: 600; color: #1e293b; font-family: monospace;">{{ paymentTradeNo }}</span>
        </div>
      </div>

      <!-- 二维码显示区域 -->
      <div style="padding: 30px; text-align: center;">
        <div style="display: inline-block; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); margin-bottom: 20px;">
          <img
            v-if="qrCodeImageUrl"
            :src="qrCodeImageUrl"
            alt="支付二维码"
            style="width: 200px; height: 200px; display: block;"
            @error="handleQrCodeError"
          />
          <div v-if="!paymentQrCode" style="width: 200px; height: 200px; display: flex; align-items: center; justify-content: center; background: #f5f5f5; color: #999; border-radius: 8px;">
            二维码URL为空
          </div>
          <div v-if="qrCodeError" style="width: 200px; height: 200px; display: flex; flex-direction: column; align-items: center; justify-content: center; background: #f5f5f5; color: #999; border-radius: 8px; gap: 12px;">
            二维码生成失败
            <button class="btn-base btn-small btn-primary" @click="retryQrCode">重试</button>
          </div>
          <div v-if="paymentQrCode && !qrCodeImageUrl && !qrCodeError" style="width: 200px; height: 200px; display: flex; flex-direction: column; align-items: center; justify-content: center; background: linear-gradient(135deg, rgba(102, 201, 179, 0.1) 0%, rgba(240, 253, 250, 0.8) 100%); color: #66c9b3; border-radius: 8px; font-size: 14px; font-weight: 500;">
            正在生成二维码...
          </div>
        </div>

        <p style="color: #666; font-size: 14px; margin: 0 0 20px 0;">请使用支付宝扫描上方二维码完成支付</p>

        <!-- 备用方案 -->
        <div style="padding: 16px; background: #f1f5f9; border-radius: 8px; border: 1px solid #e2e8f0;">
          <p style="font-size: 12px; color: #64748b; margin: 0 0 12px 0;">如果二维码无法显示，请点击下方链接：</p>
          <button class="btn-base btn-medium btn-success" @click="openPaymentUrl">
            打开支付链接
          </button>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="modal-actions">
        <button class="btn-base btn-medium btn-secondary" @click="showPaymentModal = false">
          取消支付
        </button>
        <button class="btn-base btn-medium btn-primary" @click="checkPaymentStatus">
          检查支付状态
        </button>
      </div>
    </template>
  </DesktopModal>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { useAuthStore } from '@/stores/auth'
import { apiClient, type SubscribeInfo, type Notice } from '@/api/client'
import { openClientApp, downloadClientApp, getManualImportConfig } from '@/utils/clientUtils'

import SubscriptionDialog from '@/components/SubscriptionDialog.vue'
import NoticeDetailModal from '@/components/NoticeDetailModal.vue'
import DesktopModal from '@/components/DesktopModal.vue'
import QRCode from 'qrcode'

const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()

// 状态
const loading = ref(false)
const subscriptionLoading = ref(false)

// 自动续费相关状态
const autoRenewalEnabled = ref(0)
const savingAutoRenewal = ref(false)

// 订阅信息状态
const subscribeInfo = ref<any>(null)
const loadingSubscribeInfo = ref(false)
const showRechargeModal = ref(false)
const recharging = ref(false)
const rechargeAmount = ref<number | null>(null)

// 支付相关状态
const showPaymentMethodModal = ref(false)
const showPaymentModal = ref(false) // 二维码支付模态框
const paymentMethods = ref<any[]>([])
const selectedPaymentMethod = ref<any>(null)
const paymentTradeNo = ref<string>('')
const paymentAmount = ref<number>(0)
const paymentQrCode = ref('')
const qrCodeError = ref(false)
const qrCodeImageUrl = ref('')
const selectedPlatform = ref<string>('windows')
// 移动端展开状态管理
const expandedPlatforms = ref<Set<string>>(new Set())
const isSubscriptionExpanded = ref<boolean>(false)

// 重置流量相关状态
const resettingTraffic = ref(false)
const showResetTrafficModal = ref(false)

// 未支付订单相关状态
const unpaidOrder = ref<any>(null)

// 流量使用率提醒相关状态
const showTrafficAlert = ref(false)

// 通知相关状态
const notices = ref<Notice[]>([])
const noticesLoading = ref(true)
const noticeDetailsCache = ref<Map<number, Notice>>(new Map()) // 通知详情缓存

// 功能开关
const isRechargeEnabled = computed(() => {
  return window.V2BOARD_CONFIG?.FEATURES?.RECHARGE_ENABLED !== false
})
const showNoticeModal = ref(false)
const selectedNotice = ref<Notice | null>(null)

// 订阅链接模态框相关
const showSubscriptionModal = ref(false)
const currentSubscriptionType = ref('')
const currentSubscriptionUrl = ref('')
const modalTitle = ref('')
const modalSubtitle = ref('')
const modalMode = ref('normal')
const manualInstructions = ref('')





// 平台图标SVG组件
const platformIconSvgs = {
  windows: `<svg width="20" height="20" viewBox="0 0 1024 1024" fill="currentColor">
    <path d="M0 139.392L409.429333 81.92l0.170667 407.210667-409.216 2.389333L0 139.392z m409.301333 395.818667L409.6 942.08 0 884.181333V532.48l409.301333 2.730667z m41.258667-454.186667L1024 0v487.125333l-573.44 4.394667V81.024zM1024 533.333333L1023.872 1024l-572.501333-79.274667-0.810667-412.245333 573.44 0.896z"/>
  </svg>`,
  android: `<svg width="20" height="20" viewBox="0 0 1024 1024" fill="currentColor">
    <path d="M275.2 748.8c0 21.7344 17.7664 39.4752 39.4752 39.4752h39.4752v138.1376c0 32.7424 26.4448 59.2128 59.2128 59.2128s59.2128-26.4448 59.2128-59.2128v-138.1376h78.9248v138.1376a59.136 59.136 0 0 0 59.2128 59.2128 59.136 59.136 0 0 0 59.2128-59.2128v-138.1376h39.4752c21.7344 0 39.4752-17.7408 39.4752-39.4752V354.1504h-473.6V748.8zM176.5376 354.1504a59.136 59.136 0 0 0-59.2128 59.2128v276.2496c0 32.768 26.4448 59.2128 59.2128 59.2128s59.2128-26.4192 59.2128-59.2128V413.3632a59.136 59.136 0 0 0-59.2128-59.2128z m670.9248 0a59.136 59.136 0 0 0-59.2128 59.2128v276.2496c0 32.768 26.4448 59.2128 59.2128 59.2128s59.2128-26.4192 59.2128-59.2128V413.3632a59.1616 59.1616 0 0 0-59.2128-59.2128z m-196.1728-230.4512l51.5328-51.5072a19.6864 19.6864 0 1 0-27.8528-27.8272l-58.3936 58.2144A235.3152 235.3152 0 0 0 512 77.9008a235.008 235.008 0 0 0-105.1648 24.8576l-58.624-58.624a19.6608 19.6608 0 1 0-27.8016 27.8272l51.712 51.712c-58.5984 43.2128-96.896 112.6656-96.896 191.0016h473.6c-0.0256-78.5152-38.5024-147.968-97.536-190.976z m-218.2144 112.0512h-39.4752V196.3008h39.4752v39.4496z m197.3248 0h-39.4752V196.3008h39.4752v39.4496z"/>
  </svg>`,
  ios: `<svg width="20" height="20" viewBox="0 0 1024 1024" fill="currentColor">
    <path d="M812.2 550.8c-7.2-96.1 56.4-156.8 116.4-199-56.4-50.6-105.4-99.5-209.1-104.5-83.6-3.4-121.8 43.8-200 45.5-72.7 1.7-67.3-52.3-218.2-37.1C170.5 269.2 55.9 390.6 65 560.9c9.1 183.8 147.3 438.3 276.3 438.3 90.9-1.7 112.7-42.1 187.3-42.1 92.7 0 114.5 50.6 187.3 40.5C841.4 980.7 959.5 797 959.5 738c-69.1-33.8-138.2-91.1-147.3-187.2z m-105.4-526C534.1 46.7 485 193.4 494.1 260.8c132.7 6.7 232.7-123.1 212.7-236z"/>
  </svg>`
}

// 平台名称映射
const platformNames = {
  windows: 'Windows 客户端',
  android: 'Android 客户端',
  ios: 'iOS 客户端'
}

// 平台主题色映射
const platformThemes = {
  windows: {
    primary: '#0078D4',
    secondary: '#106EBE',
    background: 'linear-gradient(135deg, rgba(0, 120, 212, 0.15) 0%, rgba(16, 110, 190, 0.25) 100%)'
  },
  android: {
    primary: '#3DDC84',
    secondary: '#00C853',
    background: 'linear-gradient(135deg, rgba(61, 220, 132, 0.15) 0%, rgba(0, 200, 83, 0.25) 100%)'
  },
  ios: {
    primary: '#007AFF',
    secondary: '#0056CC',
    background: 'linear-gradient(135deg, rgba(0, 122, 255, 0.15) 0%, rgba(0, 86, 204, 0.25) 100%)'
  }
}

// 获取当前平台的主题
const currentTheme = computed(() => {
  return platformThemes[selectedPlatform.value as keyof typeof platformThemes] || platformThemes.windows
})



// 格式化邮箱显示
const formatEmailDisplay = (email: string): string => {
  if (!email || !email.includes('@')) return email

  const [username, domain] = email.split('@')

  // 如果用户名太长，进行截断
  if (username.length > 12) {
    return username.substring(0, 12) + '...'
  }

  return username
}

// 获取邮箱域名
const getEmailDomain = (email: string): string => {
  if (!email || !email.includes('@')) return ''
  return '@' + email.split('@')[1]
}

// 复制邮箱到剪贴板
const copyEmail = async () => {
  const email = authStore.user?.email
  if (!email) return

  try {
    await navigator.clipboard.writeText(email)
    message.success('邮箱已复制到剪贴板')
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = email
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)
    message.success('邮箱已复制到剪贴板')
  }
}

// 获取订阅信息
const fetchSubscribeInfo = async () => {
  if (loadingSubscribeInfo.value) return

  loadingSubscribeInfo.value = true
  try {
    subscribeInfo.value = await apiClient.getSubscribeInfo()
  } catch (error) {
    // 静默处理错误
  } finally {
    loadingSubscribeInfo.value = false
  }
}

// 流量相关计算
const usedTraffic = computed(() => {
  // 优先使用订阅信息中的数据，如果没有则使用authStore中的数据
  if (subscribeInfo.value) {
    return (subscribeInfo.value.u || 0) + (subscribeInfo.value.d || 0)
  }
  return (authStore.user?.u || 0) + (authStore.user?.d || 0)
})

const totalTraffic = computed(() => {
  // 优先使用订阅信息中的数据
  if (subscribeInfo.value) {
    return subscribeInfo.value.transfer_enable || 0
  }
  return authStore.user?.transfer_enable || 0
})

const remainingTraffic = computed(() => {
  return Math.max(0, totalTraffic.value - usedTraffic.value)
})

const trafficPercentage = computed(() => {
  const total = totalTraffic.value
  if (total === 0) return 0
  return Math.min(100, (usedTraffic.value / total) * 100)
})

const trafficColor = computed(() => {
  const percentage = trafficPercentage.value
  if (percentage >= 90) return '#ff4757'
  if (percentage >= 70) return '#ffa502'
  return '#2ed573'
})

const trafficStatusClass = computed(() => {
  const percentage = trafficPercentage.value
  if (percentage >= 90) return 'status-danger'
  if (percentage >= 70) return 'status-warning'
  return 'status-normal'
})

const trafficStatusText = computed(() => {
  const percentage = trafficPercentage.value
  if (percentage >= 90) return '流量即将耗尽'
  if (percentage >= 70) return '流量使用较多'
  return '流量充足'
})

// 是否显示重置流量按钮
const showResetTrafficButton = computed(() => {
  // 需要满足以下条件：
  // 1. 流量使用率 >= 80%
  // 2. 订阅未过期
  // 3. 当前套餐有reset_price
  const user = authStore.user
  const plan = subscribeInfo.value?.plan

  if (!user || !plan) return false

  const trafficUsageRate = trafficPercentage.value
  const isNotExpired = !user.expired_at || user.expired_at > Date.now() / 1000
  const hasResetPrice = plan.reset_price !== null && plan.reset_price !== undefined

  return trafficUsageRate >= 80 && isNotExpired && hasResetPrice
})

// 生成分段进度弧线 - 确保颜色正确跟随弧形
const getProgressSegments = (percentage: number) => {
  const segments = []
  if (percentage <= 0) return segments

  // 将进度分成多个小段，每段5度
  const totalAngle = (percentage / 100) * 180 // 总角度
  const segmentAngle = 5 // 每段5度
  const numSegments = Math.ceil(totalAngle / segmentAngle)

  for (let i = 0; i < numSegments; i++) {
    const startAngle = i * segmentAngle
    const endAngle = Math.min((i + 1) * segmentAngle, totalAngle)

    // 计算起点和终点坐标
    const startRadians = startAngle * Math.PI / 180
    const endRadians = endAngle * Math.PI / 180

    const startX = 120 + 95 * Math.cos(Math.PI - startRadians)
    const startY = 105 - 95 * Math.sin(Math.PI - startRadians)
    const endX = 120 + 95 * Math.cos(Math.PI - endRadians)
    const endY = 105 - 95 * Math.sin(Math.PI - endRadians)

    // 生成小弧段路径
    const path = `M ${startX} ${startY} A 95 95 0 0 1 ${endX} ${endY}`

    // 根据位置计算颜色和透明度
    const segmentPercentage = ((i + 1) / numSegments) * percentage
    const opacity = 1 - (i / numSegments) * 0.3 // 渐变透明度效果

    segments.push({
      path,
      color: trafficColor.value,
      opacity
    })
  }

  return segments
}

// 获取进度点位置
const getProgressPoint = (percentage: number) => {
  const angle = (percentage / 100) * 180
  const radians = (angle * Math.PI) / 180
  const x = 120 + 95 * Math.cos(Math.PI - radians)
  const y = 105 - 95 * Math.sin(Math.PI - radians)
  return { x, y }
}

// 格式化金额
const formatMoney = (amount?: number) => {
  if (amount === undefined || amount === null) return '0.00'
  return (amount / 100).toFixed(2)
}

// 格式化流量（简短版本）
const formatTrafficShort = (bytes: number) => {
  if (!bytes) return '0GB'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const k = 1024
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  if (i >= units.length) return '∞'
  
  const value = bytes / Math.pow(k, i)
  return `${value.toFixed(i === 0 ? 0 : 1)}${units[i]}`
}

// 格式化剩余天数
const formatExpireDays = (expiredAt?: number) => {
  if (!expiredAt) return '永久'

  const now = Date.now()
  const expireTime = expiredAt * 1000
  const diffTime = expireTime - now

  if (diffTime <= 0) return '已过期'

  const days = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return `${days}天`
}

// 格式化到期时间
const formatExpireTime = (expiredAt?: number | null) => {
  if (!expiredAt) return '永久有效'

  const expireDate = new Date(expiredAt * 1000)
  const now = new Date()

  if (expireDate <= now) return '已过期'

  return expireDate.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 快捷充值金额
const quickAmounts = [10, 20, 50, 100, 200, 500]

// 检测是否为移动端布局
const shouldUseMobileLayout = () => {
  return window.innerWidth <= 768
}

// 处理充值
const handleRecharge = async () => {
  if (!rechargeAmount.value || rechargeAmount.value <= 0) {
    message.error('请输入有效的充值金额')
    return
  }

  try {
    recharging.value = true
    message.loading('正在创建充值订单...')

    // 创建充值订单
    const response = await apiClient.instance.post('/api/v1/user/order/save', {
      plan_id: 0,
      period: 'deposit',
      deposit_amount: rechargeAmount.value * 100 // 转换为分
    })

    const tradeNo = response.data.data
    message.destroyAll()
    message.success('充值订单创建成功！')

    // 关闭充值模态框
    showRechargeModal.value = false

    // 获取支付方式并直接弹出支付选择
    try {
      message.loading('正在获取支付方式...')
      const methods = await apiClient.getPaymentMethods()
      message.destroyAll()

      if (methods && methods.length > 0) {
        // 设置支付信息
        paymentTradeNo.value = tradeNo
        paymentAmount.value = rechargeAmount.value * 100 // 保存为分
        paymentMethods.value = methods

        // 显示支付方式选择模态框
        showPaymentMethodModal.value = true
      } else {
        message.error('没有可用的支付方式，请联系管理员')
        // 跳转到订单页面
        router.push(`/orders?trade_no=${tradeNo}`)
      }
    } catch (error) {
      message.destroyAll()
      message.error('获取支付方式失败，跳转到订单页面')
      // 跳转到订单页面
      router.push(`/orders?trade_no=${tradeNo}`)
    }

    // 重置充值金额
    rechargeAmount.value = null

  } catch (error: any) {
    message.destroyAll()

    let errorMessage = '充值失败，请稍后重试'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    message.error(errorMessage)
  } finally {
    recharging.value = false
  }
}

// 选择支付方式
const selectPaymentMethod = async (method: any) => {
  selectedPaymentMethod.value = method
  showPaymentMethodModal.value = false

  // 对充值订单进行支付
  if (paymentTradeNo.value) {
    await processPayment(paymentTradeNo.value, method)
  } else {
    message.error('没有可支付的订单，请重试')
  }
}

// 处理支付
const processPayment = async (tradeNo: string, method: any) => {
  try {
    message.loading('正在处理支付...')

    // 调用checkout API对已存在的订单进行支付
    const paymentResult = await apiClient.checkoutOrder(tradeNo, method.id)

    // 检查返回数据结构
    if (!paymentResult || typeof paymentResult.type === 'undefined') {
      message.destroyAll()
      message.error('支付结果数据格式错误，请重试')
      return
    }

    message.destroyAll()

    // 根据支付类型处理
    if (paymentResult.type === 0) {
      // 二维码支付
      if (!paymentResult.data) {
        message.error('二维码URL为空，请重试')
        return
      }

      // 检测是否为移动端
      if (shouldUseMobileLayout()) {
        // 移动端直接跳转
        window.location.href = paymentResult.data
        message.info('正在跳转到支付页面，请完成支付')
      } else {
        // 桌面端显示二维码模态框
        paymentQrCode.value = paymentResult.data
        qrCodeError.value = false
        qrCodeImageUrl.value = '' // 重置图片URL
        showPaymentModal.value = true

        // 异步生成二维码图片
        generateQrCodeUrl(paymentResult.data).then(imageUrl => {
          qrCodeImageUrl.value = imageUrl
        }).catch(error => {
          qrCodeError.value = true
        })

        message.info('请使用支付宝扫描二维码完成支付')
      }
    } else if (paymentResult.type === 1) {
      // 跳转支付
      if (!paymentResult.data) {
        message.error('跳转URL为空，请重试')
        return
      }

      window.open(paymentResult.data, '_blank')
      message.info('已打开支付页面，请完成支付')
    } else if (paymentResult.type === -1) {
      // 免费订单或余额支付成功
      message.success('充值成功！')

      // 刷新用户信息
      await authStore.fetchUserInfo()

      // 不跳转，留在当前页面
      return
    } else if (paymentResult.type === 2) {
      // 余额部分支付，需要继续支付剩余金额
      message.success('已使用余额支付部分金额，请选择其他支付方式完成剩余支付')

      // 获取剩余需要支付的金额
      const remainingAmount = paymentResult.data || 0

      if (remainingAmount > 0) {
        message.info(`剩余需要支付：¥${(remainingAmount / 100).toFixed(2)}`)

        // 获取支付方式并直接弹出选择模态框
        try {
          const methods = await apiClient.getPaymentMethods()
          if (methods && methods.length > 0) {
            // 更新支付金额和订单信息（保持分为单位）
            paymentAmount.value = remainingAmount
            paymentTradeNo.value = tradeNo

            // 直接显示支付方式选择（不包含余额支付）
            paymentMethods.value = methods
            showPaymentMethodModal.value = true
          } else {
            message.error('没有其他支付方式可用，请联系管理员')
          }
        } catch (error) {
          message.error('获取支付方式失败，请重试')
        }
      } else {
        // 余额已完全支付
        message.success('充值成功！')
        await authStore.fetchUserInfo()
      }
    } else {
      // 其他类型
      message.error('未知的支付类型，请重试')
    }

  } catch (error: any) {
    message.destroyAll()

    let errorMessage = '支付处理失败，请稍后重试'
    if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    message.error(errorMessage)

    // 支付失败时跳转到订单页面
    router.push(`/orders?trade_no=${tradeNo}`)
  }
}

// 生成二维码图片URL（使用本地生成）
const generateQrCodeUrl = async (url: string) => {
  try {
    // 使用本地qrcode库生成base64格式的二维码
    const qrCodeDataUrl = await QRCode.toDataURL(url, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      errorCorrectionLevel: 'M'
    })
    return qrCodeDataUrl
  } catch (error) {
    // 如果本地生成失败，回退到外部服务
    const encodedUrl = encodeURIComponent(url)
    return `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodedUrl}`
  }
}

// 重试生成二维码
const retryQrCode = async () => {
  qrCodeError.value = false
  qrCodeImageUrl.value = ''

  if (paymentQrCode.value) {
    try {
      const imageUrl = await generateQrCodeUrl(paymentQrCode.value)
      qrCodeImageUrl.value = imageUrl
    } catch (error) {
      qrCodeError.value = true
    }
  }
}

// 打开支付链接
const openPaymentUrl = () => {
  if (!paymentQrCode.value) {
    message.error('支付链接为空')
    return
  }

  window.open(paymentQrCode.value, '_blank')
  message.info('已在新窗口打开支付链接')
}

// 检查支付状态
const checkPaymentStatus = async () => {
  if (!paymentTradeNo.value) return

  try {
    message.loading('正在检查支付状态...')

    const status = await apiClient.checkOrderStatus(paymentTradeNo.value)

    message.destroyAll()

    if (status === 1 || status === 3) {
      // 支付成功
      message.success('支付成功！')
      showPaymentModal.value = false

      // 刷新用户信息
      await authStore.fetchUserInfo()
    } else if (status === 0) {
      // 仍未支付
      message.warning('订单尚未支付，请继续扫码支付')
    } else if (status === 2) {
      // 订单已取消
      message.error('订单已取消')
      showPaymentModal.value = false
    } else {
      message.info('订单状态未知，请稍后再试')
    }

  } catch (error: any) {
    message.destroyAll()

    const errorMessage = error.response?.data?.message || error.message || '检查支付状态失败'
    message.error(`检查支付状态失败: ${errorMessage}`)
  }
}

// 处理二维码加载错误
const handleQrCodeError = () => {
  qrCodeError.value = true
  message.error('二维码生成失败，请尝试点击下方按钮直接打开支付链接')
}

// 平台选择
const selectPlatform = (platform: string) => {
  selectedPlatform.value = platform
}

// 移动端平台展开切换
const toggleMobilePlatformExpanded = (platform: string) => {
  const expanded = expandedPlatforms.value
  if (expanded.has(platform)) {
    expanded.delete(platform)
  } else {
    expanded.add(platform)
  }
  // 触发响应式更新
  expandedPlatforms.value = new Set(expanded)
}

// 检查移动端平台是否展开
const isMobilePlatformExpanded = (platform: string) => {
  return expandedPlatforms.value.has(platform)
}

// 统一的平台点击处理
const handlePlatformClick = (platform: string) => {
  // 检查是否为移动端
  const isMobile = window.innerWidth <= 768

  if (isMobile) {
    // 移动端：切换展开状态
    toggleMobilePlatformExpanded(platform)
  } else {
    // 桌面端：选择平台
    selectPlatform(platform)
  }
}

// 订阅链接展开切换
const toggleSubscriptionExpanded = () => {
  isSubscriptionExpanded.value = !isSubscriptionExpanded.value
}





// 获取平台标题
const getPlatformTitle = (platform: string) => {
  const name = platformNames[platform as keyof typeof platformNames] || '客户端'
  return name
}

// 获取平台图标SVG
const getPlatformIconSvg = (platform: string) => {
  return platformIconSvgs[platform as keyof typeof platformIconSvgs] || platformIconSvgs.windows
}

// 获取平台对应的应用（从运行时配置文件）
const getPlatformApps = (platform: string) => {
  const config = (window as any).V2BOARD_CONFIG
  if (!config || !config.CLIENTS || !config.CLIENTS[platform]) {
    return []
  }

  // 将对象转换为数组，并添加key信息
  const platformClients = config.CLIENTS[platform]
  return Object.keys(platformClients).map(key => ({
    key,
    ...platformClients[key]
  }))
}



// 加载订阅信息
const loadSubscribeInfo = async () => {
  subscriptionLoading.value = true
  try {
    subscribeInfo.value = await apiClient.getSubscribe()

    // 初始化自动续费状态
    initAutoRenewalStatus()
  } catch (error: any) {
    message.error(error.response?.data?.message || '加载订阅信息失败')
  } finally {
    subscriptionLoading.value = false
  }
}

// 初始化自动续费状态
const initAutoRenewalStatus = () => {
  if (authStore.user) {
    autoRenewalEnabled.value = authStore.user.auto_renewal ?? 0
  }
}

// 处理自动续费开关变化
const handleAutoRenewalChange = async (value: number) => {
  savingAutoRenewal.value = true
  try {
    await apiClient.updateUserSettings({
      auto_renewal: value
    })

    // 更新本地用户信息
    if (authStore.user) {
      authStore.user.auto_renewal = value
      localStorage.setItem('v2board_user', JSON.stringify(authStore.user))
    }

    message.success(value === 1 ? '自动续费已开启' : '自动续费已关闭')
  } catch (error: any) {
    // 如果保存失败，恢复原来的状态
    autoRenewalEnabled.value = autoRenewalEnabled.value === 1 ? 0 : 1
    const errorMsg = error.response?.data?.message || '设置自动续费失败'
    message.error(errorMsg)
  } finally {
    savingAutoRenewal.value = false
  }
}

// 检查未支付订单
const checkUnpaidOrders = async () => {
  try {
    const orders = await apiClient.getOrders(0) // 获取状态为0的订单
    if (orders && orders.length > 0) {
      // 取最新的未支付订单
      unpaidOrder.value = orders[0]
    }
  } catch (error: any) {
    // 静默处理错误
  }
}

// 前往支付页面
const goToPayment = () => {
  if (unpaidOrder.value) {
    router.push(`/orders?trade_no=${unpaidOrder.value.trade_no}`)
  }
}

// 关闭提醒
const dismissAlert = () => {
  unpaidOrder.value = null
}

// 关闭流量提醒
const dismissTrafficAlert = () => {
  showTrafficAlert.value = false
  // 可以在这里添加本地存储，避免重复提醒
  localStorage.setItem('traffic_alert_dismissed', Date.now().toString())
}

// 检查是否需要显示流量使用率提醒
const checkTrafficAlert = () => {
  const user = authStore.user
  const plan = subscribeInfo.value?.plan

  if (!user || !plan) return

  // 检查流量使用率是否 >= 80%
  const trafficUsageRate = trafficPercentage.value
  const isNotExpired = !user.expired_at || user.expired_at > Date.now() / 1000

  // 检查是否在24小时内已经关闭过提醒
  const lastDismissed = localStorage.getItem('traffic_alert_dismissed')
  const shouldShowAlert = !lastDismissed || (Date.now() - parseInt(lastDismissed)) > 24 * 60 * 60 * 1000

  // 显示条件：流量使用率 >= 80% && 订阅未过期 && 24小时内未关闭过
  if (trafficUsageRate >= 80 && isNotExpired && shouldShowAlert) {
    showTrafficAlert.value = true
  }
}

// 显示重置流量模态框
const openResetTrafficModal = () => {
  const plan = subscribeInfo.value?.plan
  if (!plan || plan.reset_price === null || plan.reset_price === undefined) {
    message.error('当前套餐不支持重置流量')
    return
  }
  showResetTrafficModal.value = true
}

// 确认重置流量
const confirmResetTraffic = async () => {
  const plan = subscribeInfo.value?.plan
  const user = authStore.user

  if (!plan || plan.reset_price === null || plan.reset_price === undefined) {
    message.error('当前套餐不支持重置流量')
    return
  }

  if (!user?.plan_id) {
    message.error('未找到有效订阅信息')
    return
  }

  try {
    resettingTraffic.value = true
    message.loading('正在创建重置流量订单...')

    // 按照umi.js的方式创建重置流量订单
    const orderData = {
      period: 'reset_price',
      plan_id: user.plan_id  // 使用user.plan_id，与umi.js保持一致
    }

    const response = await apiClient.createOrder(orderData)
    message.destroyAll()

    // 关闭模态框
    showResetTrafficModal.value = false

    // 如果是免费重置（价格为0），直接完成支付
    if (plan.reset_price === 0) {
      message.loading('正在激活免费重置...')
      try {
        await apiClient.checkoutOrder(response, 0)
        message.destroyAll()
        message.success('流量重置成功！')

        // 刷新用户信息和订阅信息
        await authStore.fetchUserInfo()
        await loadSubscribeInfo()
        return
      } catch (error) {
        message.destroyAll()
        message.error('自动激活失败，跳转到支付页面')
      }
    }

    // 付费重置，跳转到订单页面进行支付
    router.push(`/orders?trade_no=${response}`)

  } catch (error: any) {
    message.destroyAll()
    const errorMsg = error.response?.data?.message || '创建重置流量订单失败'
    message.error(errorMsg)
    console.error('重置流量失败:', error)
  } finally {
    resettingTraffic.value = false
  }
}

// 获取周期文本
const getPeriodText = (period: string) => {
  const periodMap: Record<string, string> = {
    'month_price': '月付',
    'quarter_price': '季付',
    'half_year_price': '半年付',
    'year_price': '年付',
    'two_year_price': '两年付',
    'three_year_price': '三年付',
    'onetime_price': '一次性',
    'reset_price': '重置'
  }
  return periodMap[period] || period
}



// 下载客户端应用
const openClientAppHandler = (app: any) => {
  downloadClientApp(app)
}

// 打开订阅链接模态框
const openSubscriptionModal = (type: string) => {
  if (!subscribeInfo.value?.subscribe_url) {
    message.error('订阅链接不可用')
    return
  }

  currentSubscriptionType.value = type
  currentSubscriptionUrl.value = getSubscriptionUrlByType(type)
  modalTitle.value = getModalTitle(type)
  modalSubtitle.value = getModalSubtitle(type)
  modalMode.value = 'normal'  // 重置为普通模式
  manualInstructions.value = ''  // 清空手动指导
  showSubscriptionModal.value = true
}

// 根据类型获取订阅链接
const getSubscriptionUrlByType = (type: string) => {
  const baseUrl = subscribeInfo.value?.subscribe_url || ''

  switch (type) {
    case 'clash-verge':
      return baseUrl + '&flag=verge'
    case 'clash-meta':
      return baseUrl + '&flag=meta'
    case 'flclash':
      return baseUrl + '&flag=meta'
    case 'clash-nyanpasu':
      return baseUrl + '&flag=meta'
    case 'stash':
      return baseUrl + '&flag=meta'
    case 'v2ray':
      return baseUrl + '&flag=v2ray'
    case 'shadowrocket':
      return baseUrl + '&flag=shadowrocket'
    case 'singbox':
      return baseUrl + '&flag=singbox'
    case 'hiddify':
      return baseUrl + '&flag=sing'  // Hiddify使用Singbox协议
    case 'quantumult':
      return baseUrl + '&flag=quantumult'
    case 'surge':
      return baseUrl + '&flag=surge'
    case 'loon':
      return baseUrl + '&flag=loon'
    default:
      return baseUrl
  }
}

// 获取模态框标题和副标题
const getModalTitle = (type: string) => {
  const titles = {
    general: '通用订阅链接',
    'clash-verge': 'Clash Verge 订阅链接',
    'clash-meta': 'ClashMeta 订阅链接',
    flclash: 'FlClash 订阅链接',
    'clash-nyanpasu': 'Clash Nyanpasu 订阅链接',
    stash: 'Stash 订阅链接',
    v2ray: 'V2Ray 订阅链接',
    shadowrocket: 'Shadowrocket 订阅链接',

    singbox: 'SingBox 订阅链接',
    hiddify: 'Hiddify 订阅链接',
    quantumult: 'QuantumultX 订阅链接',
    surge: 'Surge 订阅链接',
    loon: 'Loon 订阅链接'
  }
  return titles[type as keyof typeof titles] || '订阅链接'
}

const getModalSubtitle = (type: string) => {
  const subtitles = {
    general: '适用于大部分客户端',
    'clash-verge': '适用于 Clash Verge 客户端',
    'clash-meta': '适用于 ClashMeta 系列客户端',
    flclash: '适用于 FlClash 客户端',
    'clash-nyanpasu': '适用于 Clash Nyanpasu 客户端',
    stash: '适用于 Stash 客户端',
    v2ray: '适用于 V2Ray 系列客户端',
    shadowrocket: '适用于 Shadowrocket 客户端',

    singbox: '适用于 SingBox 系列客户端',
    hiddify: '适用于 Hiddify 系列客户端',
    quantumult: '适用于 QuantumultX 客户端',
    surge: '适用于 Surge 系列客户端',
    loon: '适用于 Loon 系列客户端'
  }
  return subtitles[type as keyof typeof subtitles] || ''
}

const getClientIcon = (type: string) => {
  const icons = {
    general: '', // 通用订阅使用默认SVG图标
    'clash-verge': '/icons/clients/clash-verge.png',
    'clash-meta': '/icons/clients/clash-meta.png',
    flclash: '/icons/clients/flclash.png',
    'clash-nyanpasu': '/icons/clients/clash-nyanpasu.png',
    stash: '/icons/clients/stash.png',
    v2ray: '/icons/clients/v2ray.png',
    shadowrocket: '/icons/clients/shadowrocket.png',
    singbox: '/icons/clients/singbox.png',
    hiddify: '/icons/clients/hiddify.png',
    quantumult: '/icons/clients/quantumult.png',
    surge: '/icons/clients/surge.png',
    loon: '/icons/clients/loon.png'
  }
  return icons[type as keyof typeof icons] || ''
}



// 获取支持的客户端（基于V2Board原版umi.js扩展）
const getSupportedClients = (type: string) => {
  // 客户端图标映射（确保使用本地图标）
  const clientIconMap = {
    'V2rayN': '/icons/clients/v2ray.png',
    'V2rayNG': '/icons/clients/v2ray.png',
    'Clash Verge': '/icons/clients/clash-verge.png',
    'ClashMeta': '/icons/clients/clash-meta.png',
    'ClashX': '/icons/clients/clash-meta.png',
    'Clash Nyanpasu': '/icons/clients/clash-nyanpasu.png',
    'FlClash': '/icons/clients/flclash.png',
    'Stash': '/icons/clients/stash.png',
    'SingBox': '/icons/clients/singbox.png',
    'NekoBox for Android': '/icons/clients/clash-meta.png',
    'ClashMeta For Android': '/icons/clients/clash-meta.png',
    'Shadowrocket': '/icons/clients/shadowrocket.png',
    'QuantumultX': '/icons/clients/quantumult.png',
    'Surge': '/icons/clients/surge.png',
    'Surfboard': '/icons/clients/surge.png',
    'Shadowrocket': '/icons/clients/shadowrocket.png',
    'Loon': '/icons/clients/loon.png',
    'hiddify': '/icons/clients/hiddify.png'
  }

  // 根据订阅类型返回对应的客户端（参考V2Board原版umi.js）
  const supportedClientNames = {
    general: [], // 通用订阅不支持一键导入，需要手动配置
    'clash-verge': ['Clash Verge'], // Clash Verge专用
    'clash-meta': ['ClashMeta'], // ClashMeta专用
    flclash: ['FlClash'], // FlClash专用
    'clash-nyanpasu': ['Clash Nyanpasu'], // Clash Nyanpasu专用
    stash: ['Stash'], // Stash专用
    v2ray: ['V2rayNG'], // V2Ray系列（移除V2rayN，桌面软件不支持URL scheme）
    shadowrocket: ['Shadowrocket'], // Shadowrocket专用
    singbox: ['SingBox'], // SingBox系列（移除NekoBox，支持不稳定）
    hiddify: ['hiddify'], // Hiddify使用Singbox协议
    quantumult: [], // QuantumultX一键导入复杂，暂时移除
    surge: ['Surge'], // Surge专用
    loon: ['Loon'] // Loon专用
  }

  const clientNames = supportedClientNames[type as keyof typeof supportedClientNames] || []

  // 直接构建客户端列表，确保图标正确
  const supportedClients = clientNames.map(name => ({
    name,
    img: clientIconMap[name as keyof typeof clientIconMap] || '/icons/clients/default.png',
    key: name.toLowerCase().replace(/\s+/g, '-')
  }))

  return supportedClients
}

// 检查客户端是否支持该订阅类型
const isClientSupported = (clientName: string, type: string) => {
  const supportMap = {
    general: ['V2rayN', 'NekoBox for Android', 'SingBox', 'Shadowrocket', 'Quantumult X'],
    clash: ['Clash Verge', 'FlClash', 'Stash'],
    v2ray: ['V2rayN', 'NekoBox for Android', 'SingBox'],
    shadowsocks: ['Shadowrocket', 'Quantumult X', 'Surge']
  }

  return supportMap[type as keyof typeof supportMap]?.includes(clientName) || false
}

// 处理复制订阅链接
const handleCopySubscription = async (url: string) => {
  try {
    await navigator.clipboard.writeText(url)
    message.success('订阅链接已复制到剪贴板')
  } catch (error) {
    message.error('复制失败，请手动复制')
  }
}

// 处理导入到客户端
const handleImportToClient = (client: any, url: string) => {
  openClientApp(client, url, (config) => {
    // 如果需要手动导入，更新模态框为手动模式
    modalTitle.value = config.title
    modalSubtitle.value = config.subtitle
    modalMode.value = config.mode
    manualInstructions.value = config.manualInstructions
    currentSubscriptionUrl.value = config.subscriptionUrl
    // 模态框已经打开，只需要切换模式
  })
}

// 导入到客户端（保留原方法兼容性）
const importToClient = (client: any) => {
  if (!client.supported) {
    message.warning(`${client.name} 不支持此订阅类型`)
    return
  }

  openClientApp(client, currentSubscriptionUrl.value)
  showSubscriptionModal.value = false
}



// 加载通知列表
const loadNotices = async () => {
  try {
    noticesLoading.value = true
    const response = await apiClient.getNotices(1, 3) // 获取最新3条通知
    notices.value = response.data || []

    // 预加载通知详情到缓存
    if (notices.value.length > 0) {
      // 并行加载所有通知的详情
      const detailPromises = notices.value.map(async (notice) => {
        try {
          const detail = await apiClient.getNoticeDetail(notice.id)
          noticeDetailsCache.value.set(notice.id, detail)
        } catch (error) {
          // 如果获取详情失败，将列表数据作为缓存
          noticeDetailsCache.value.set(notice.id, notice)
        }
      })

      // 等待所有详情加载完成（不阻塞UI）
      Promise.all(detailPromises).catch(() => {
        // 忽略错误，不影响主流程
      })
    }
  } catch (error) {
    notices.value = []
  } finally {
    noticesLoading.value = false
  }
}

// 格式化通知时间
const formatNoticeTime = (timestamp: number) => {
  const now = Date.now() / 1000
  const diff = now - timestamp

  if (diff < 60) {
    return '刚刚'
  } else if (diff < 3600) {
    return `${Math.floor(diff / 60)}分钟前`
  } else if (diff < 86400) {
    return `${Math.floor(diff / 3600)}小时前`
  } else if (diff < 2592000) {
    return `${Math.floor(diff / 86400)}天前`
  } else {
    const date = new Date(timestamp * 1000)
    return date.toLocaleDateString()
  }
}

// 截取通知内容 - 支持HTML
const truncateContent = (content: string, maxLength: number = 50) => {
  if (!content) return ''

  // 如果内容包含HTML标签，先提取纯文本长度进行判断
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = content
  const textContent = tempDiv.textContent || tempDiv.innerText || ''

  // 如果纯文本长度不超过限制，返回原始HTML
  if (textContent.length <= maxLength) {
    return content
  }

  // 如果超过限制，截取纯文本并添加省略号
  const truncatedText = textContent.substring(0, maxLength) + '...'

  // 检查原内容是否包含HTML标签
  if (content !== textContent) {
    // 包含HTML，尝试保留简单的格式
    // 对于复杂情况，返回截取的纯文本
    return truncatedText
  } else {
    // 纯文本，直接返回截取结果
    return truncatedText
  }
}

// 处理通知内容点击（阻止链接点击时打开模态框）
const handleNoticeContentClick = (event: Event) => {
  // 检查点击的是否是链接
  const target = event.target as HTMLElement
  if (target.tagName === 'A' || target.closest('a')) {
    // 如果点击的是链接，阻止事件冒泡，让链接正常工作
    event.stopPropagation()
    return
  }
  // 如果不是链接，不阻止事件冒泡，让事件继续传播到父元素
  // 这样点击文字时仍然可以触发 handleNoticeClick
}

// 处理通知点击
const handleNoticeClick = async (notice: Notice) => {
  // 优先使用缓存中的详情
  const cachedDetail = noticeDetailsCache.value.get(notice.id)
  if (cachedDetail) {
    selectedNotice.value = cachedDetail
    showNoticeModal.value = true
    return
  }

  // 如果缓存中没有，则实时获取
  try {
    const noticeDetail = await apiClient.getNoticeDetail(notice.id)
    // 获取成功后更新缓存
    noticeDetailsCache.value.set(notice.id, noticeDetail)
    selectedNotice.value = noticeDetail
    showNoticeModal.value = true
  } catch (error) {
    // 如果获取详情失败，使用列表中的数据
    selectedNotice.value = notice
    showNoticeModal.value = true
  }
}

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 处理图片加载成功
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.opacity = '1'
}



// 响应式布局处理
const handleResize = () => {
  const mainLayout = document.querySelector('.main-layout')
  if (!mainLayout) return

  const containerWidth = mainLayout.parentElement?.clientWidth || 0
  const minRequiredWidth = 900 // 最小需要的宽度

  if (containerWidth < minRequiredWidth) {
    mainLayout.classList.add('force-vertical')
  } else {
    mainLayout.classList.remove('force-vertical')
  }
}

const initResponsiveLayout = () => {
  handleResize()
  window.addEventListener('resize', handleResize)
}

// 初始化
onMounted(async () => {
  if (!authStore.isAuthenticated) {
    router.push('/login')
    return
  }

  // 初始化响应式布局
  initResponsiveLayout()

  loading.value = true
  try {
    // 并行执行所有数据加载，包括强制刷新用户信息
    await Promise.all([
      authStore.fetchUserInfo(), // 每次都强制刷新用户信息
      fetchSubscribeInfo(), // 获取订阅信息（包含正确的u和d字段）
      loadSubscribeInfo(),
      loadNotices(),
      checkUnpaidOrders()
    ])

    // 数据加载完成后检查流量提醒
    checkTrafficAlert()
  } catch (error) {
    message.error('页面加载失败，请刷新重试')
  } finally {
    loading.value = false
  }
})

// 清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
@import '@/styles/components/buttons.scss';

.dashboard-page {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 0;
}

/* 全局加载状态 */
.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.dashboard-content {
  max-width: 100%;
  margin: 0;
  padding: 20px;
}

/* 流量使用率提醒样式 */
.traffic-usage-alert {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.traffic-usage-alert .alert-details .reset-link {
  margin-left: 8px;
  color: #fbbf24;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.traffic-usage-alert .alert-details .reset-link:hover {
  color: #f59e0b;
}

/* 未支付订单提醒样式 */
.unpaid-order-alert {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8e8e 100%);
  border-radius: 12px;
  padding: 16px 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 16px rgba(255, 107, 107, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.alert-icon {
  color: white;
  flex-shrink: 0;
}

.alert-info {
  flex: 1;
  color: white;
  background: transparent !important; /* 确保没有绿色背景 */
}

.alert-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: transparent !important; /* 确保标题没有绿色背景 */
}

.alert-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  opacity: 0.95;
  background: transparent !important; /* 确保详情没有绿色背景 */
}

.order-plan {
  font-weight: 500;
}

.order-period {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.order-amount {
  font-weight: 600;
  font-size: 16px;
}

.alert-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 专业CSS Grid布局 - 彻底解决空间分配问题 */
.main-layout {
  display: grid;
  grid-template-columns: minmax(400px, 1fr) minmax(500px, 1fr);
  gap: clamp(16px, 2vw, 24px);
  align-items: start; /* 恢复顶部对齐，因为现在两个容器都是固定高度 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* 当空间不足时，自动切换到单列布局 */
  grid-auto-flow: row;
}

.left-column {
  display: flex;
  flex-direction: column;
  gap: clamp(12px, 1.5vw, 20px);
  min-width: 0;
  width: 100%;
}

.right-column {
  display: flex;
  flex-direction: column;
  gap: clamp(12px, 1.5vw, 20px);
  /* 现代响应式：使用clamp()替代媒体查询 */
  min-width: clamp(0px, 50vw, 500px); /* 自适应最小宽度：小屏幕0px，大屏幕500px */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* 确保在小屏幕上不溢出 */
  overflow: hidden;
}

/* 右侧内容区域专用优化 */
.right-column .notice-section,
.right-column .traffic-dashboard {
  min-width: 0;
  width: 100%;
  overflow: visible;
  box-sizing: border-box;
}

/* 确保右侧卡片内容不被截断 */
.right-column .notice-section .notice-content,
.right-column .traffic-dashboard .traffic-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  min-width: 0;
}



/* 现代浏览器容器查询支持 */
@supports (container-type: inline-size) {
  .main-layout {
    container-type: inline-size;
  }

  @container (max-width: 900px) {
    .main-layout {
      display: flex !important;
      flex-direction: column !important;
      grid-template-columns: none !important;
    }
  }
}

/* JavaScript动态布局控制 */
.main-layout.force-vertical {
  display: flex !important;
  flex-direction: column !important;
  grid-template-columns: none !important;
  gap: 20px !important;
}

.main-layout.force-vertical .left-column,
.main-layout.force-vertical .right-column {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* 订阅链接容器 */
.subscription-container {
  background: linear-gradient(145deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(255, 255, 255, 0.95) 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
}

/* 用户信息大卡片 */
.user-card {
  background: linear-gradient(180deg,
    #66c9b3 0%,
    #84d2c1 33%,
    #aae0d5 66%,
    #c0e8df 100%);
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 重要通知容器 - 与用户信息容器高度一致 */
.notice-section {
  background: linear-gradient(135deg,
    rgba(102, 201, 179, 0.12) 0%,
    rgba(132, 210, 193, 0.15) 25%,
    rgba(170, 224, 213, 0.18) 50%,
    rgba(192, 232, 223, 0.20) 75%,
    rgba(240, 253, 250, 0.25) 100%);
  border-radius: 24px;
  padding: 28px;
  box-shadow:
    0 8px 32px rgba(102, 201, 179, 0.08),
    0 4px 16px rgba(102, 201, 179, 0.06),
    0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(102, 201, 179, 0.15);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  /* 确保容器宽度正确 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  /* 固定高度与用户信息容器完全一致 */
  height: 300px;
}

/* 左下部分 */
.left-bottom {
  grid-area: left-bottom;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 流量仪表盘 */
.traffic-dashboard {
  background: linear-gradient(145deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(255, 255, 255, 0.95) 100%);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.08),
    0 4px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.8);
  height: fit-content;
  /* 确保容器能包含所有内容 */
  overflow: hidden;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* 仪表盘图表视觉部分 */
.gauge-visual {
  flex-shrink: 0;
  transform: scale(0.85);
  transform-origin: center;
}

/* 中等屏幕优化（包括1366x768等） - 智能适应方案 */
@media screen and (min-width: 1200px) and (max-width: 1600px) {
  .traffic-dashboard .traffic-gauge-container {
    /* 让容器自动决定是否换行 */
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
    padding: 16px;
  }

  .traffic-dashboard .traffic-gauge-container .gauge-visual {
    /* 仪表盘适中缩放 */
    transform: scale(0.85);
    flex-shrink: 0;
  }

  .traffic-dashboard .traffic-gauge-container .traffic-stats-cards {
    /* 确保卡片区域有合适的最小宽度 */
    min-width: 300px;
    max-width: 400px;
    flex: 1 1 300px;
  }
}

.user-main-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 20px;
}



/* 用户邮箱容器 */
.user-email-container {
  margin-bottom: 16px;
}

.user-greeting {
  margin-bottom: 4px;
}

.greeting-text {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.user-email-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px 8px;
  margin: 0 -8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  position: relative;
  width: fit-content;
}

.user-email-wrapper:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-email-wrapper:hover .copy-hint {
  opacity: 1;
  transform: translateX(0);
}

.user-email-main {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  letter-spacing: 0.5px;
  line-height: 1.2;
}

.user-email-domain {
  font-size: 16px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3px;
}

.copy-hint {
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.7);
}

.user-badges {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  flex-wrap: wrap;
}

.user-status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: fit-content;
  transition: all 0.3s ease;
}

.user-status-badge:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-status-badge.verified svg {
  color: #10b981;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.user-status-badge.member {
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.2), rgba(255, 193, 7, 0.3));
  border-color: rgba(255, 215, 0, 0.3);
}

.user-status-badge.member svg {
  color: #ffd700;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.subscription-status {
  margin-bottom: 16px;
}

.plan-name {
  color: #FFD700;
  font-weight: 500;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.plan-icon {
  width: 16px;
  height: 16px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.no-subscription {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.free-icon {
  width: 16px;
  height: 16px;
  opacity: 0.8;
}

/* 订阅信息容器样式 */
.subscription-items {
  margin-top: 8px;
}

/* 用户订阅信息项样式 */
.user-subscription-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: linear-gradient(135deg,
    rgba(102, 201, 179, 0.4) 0%,
    rgba(132, 210, 193, 0.35) 50%,
    rgba(170, 224, 213, 0.3) 100%);
  backdrop-filter: blur(10px);
  border-radius: 6px;
  padding: 4px 8px;
  margin-bottom: 3px;
  border: 1px solid rgba(102, 201, 179, 0.4);
  box-shadow:
    0 1px 3px rgba(102, 201, 179, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.user-subscription-item:hover {
  background: linear-gradient(135deg,
    rgba(102, 201, 179, 0.5) 0%,
    rgba(132, 210, 193, 0.45) 50%,
    rgba(170, 224, 213, 0.4) 100%);
  transform: translateY(-0.5px);
  box-shadow:
    0 1px 4px rgba(102, 201, 179, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(102, 201, 179, 0.5);
}

.subscription-label {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  font-size: 11px;
  flex-shrink: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.subscription-value {
  color: white;
  font-weight: 600;
  font-size: 11px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  text-align: right;
  margin-left: 6px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(132, 210, 193, 0.2) 100%);
  padding: 1px 6px;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.25);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
}

/* 用户订阅信息数值特殊样式 */
.user-subscription-item .subscription-value {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.15) 100%) !important;
  border: 1px solid rgba(255, 255, 255, 0.35) !important;
  color: white !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !important;
}

/* 订阅值容器 - 支持并排显示套餐名称和重置按钮 */
.subscription-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 重置流量模态框样式 */
.reset-traffic-modal-content {
  padding: 20px 0;
}

.reset-info {
  margin-bottom: 24px;
}

.reset-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.reset-info-item:last-child {
  border-bottom: none;
}

.reset-info-item .label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.reset-info-item .value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

.reset-info-item .value.price {
  color: #f59e0b;
  font-size: 16px;
}

.reset-warning {
  background: linear-gradient(135deg,
    rgba(245, 158, 11, 0.1) 0%,
    rgba(251, 191, 36, 0.1) 100%);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 12px;
  padding: 16px;
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.warning-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.warning-text {
  flex: 1;
}

.warning-text p {
  margin: 0 0 8px 0;
  font-size: 13px;
  line-height: 1.5;
  color: #666;
}

.warning-text p:last-child {
  margin-bottom: 0;
}

.warning-text strong {
  color: #f59e0b;
  font-weight: 600;
}

.modal-footer-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-footer-buttons .btn-base {
  min-width: 80px;
}

/* 重置流量按钮样式 - 紧凑型设计，与套餐名称并排 */
.reset-traffic-btn {
  background: linear-gradient(135deg,
    rgba(255, 193, 7, 0.8) 0%,
    rgba(255, 152, 0, 0.9) 100%) !important;
  border: 1px solid rgba(255, 193, 7, 0.9) !important;
  color: #fff !important;
  font-weight: 600 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 3px;
  justify-content: center;
  font-size: 10px;
  padding: 3px 6px;
  border-radius: 3px;
  min-height: 20px;
  white-space: nowrap;
  flex-shrink: 0;
}

.reset-traffic-btn:hover:not(:disabled) {
  background: linear-gradient(135deg,
    rgba(255, 193, 7, 0.9) 0%,
    rgba(255, 152, 0, 1) 100%) !important;
  border-color: rgba(255, 193, 7, 1) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(255, 193, 7, 0.4);
}

.reset-traffic-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  background: linear-gradient(135deg,
    rgba(255, 193, 7, 0.5) 0%,
    rgba(255, 152, 0, 0.6) 100%) !important;
}

.loading-spinner-small {
  width: 12px;
  height: 12px;
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-top: 2px solid #000;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 自动续费开关样式 */
.auto-renewal-item {
  position: relative;
}

.auto-renewal-switch {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2px 6px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.auto-renewal-switch :deep(.n-switch) {
  --n-rail-color: rgba(255, 255, 255, 0.2);
  --n-rail-color-active: #66c9b3;
  --n-button-color: #ffffff;
  --n-button-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  --n-box-shadow-focus: 0 0 0 2px rgba(102, 201, 179, 0.3);
}

.auto-renewal-switch :deep(.n-switch.n-switch--active) {
  --n-rail-color-active: #66c9b3;
}

.auto-renewal-switch :deep(.n-switch__button) {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.buy-button {
  border-radius: 8px;
}

/* 用户信息卡片容器 - 左右布局，保持原有样式 */
.user-card-container {
  display: flex;
  gap: 20px;
  background: linear-gradient(180deg,
    #66c9b3 0%,
    #84d2c1 33%,
    #aae0d5 66%,
    #c0e8df 100%);
  border-radius: 24px;
  padding: 28px;
  color: white;
  position: relative;
  overflow-y: visible; /* 让内容自然流动 */
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(102, 201, 179, 0.3);
  /* 固定高度与重要通知容器一致 */
  height: 300px;
}

/* 左侧用户信息区域 */
.user-main-info {
  flex: 1;
  min-width: 0;
}

/* 用户信息容器三个小卡片 - 智能响应式布局 */
.balance-cards-grid-layout {
  display: grid;
  grid-template-columns: 1fr 1fr; /* 默认2+1布局，适合中等分辨率 */
  gap: 8px;
  min-width: 0;
  max-width: none;
  flex-shrink: 1;
  width: 100%;
  margin-right: -8px; /* 减小右边与容器边界的间距 */
}

/* 高分辨率：一行显示3个卡片，限制最大宽度 */
@media (min-width: 1600px) {
  .balance-cards-grid-layout {
    grid-template-columns: repeat(3, minmax(120px, 180px)); /* 3列，每列最大180px */
    justify-content: start; /* 左对齐，避免拉伸 */
  }
}



/* 小屏幕全垂直布局 */
@media (max-width: 768px) {
  .balance-cards-grid-layout {
    display: flex !important;
    flex-direction: column !important;
    max-width: none !important;
    width: 100% !important;
  }

  .balance-cards-grid-layout .balance-card-new {
    width: 100% !important;
    min-width: 100% !important;
  }
}

/* 用户信息容器三个小卡片 - Grid自适应样式 */
.balance-card-new {
  position: relative;
  width: 100%;
  height: 100px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.06),
    0 2px 4px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  cursor: pointer;
  overflow: hidden;
}





.balance-card-new::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  z-index: 1;
}

/* 佣金余额卡片 - 橙色主题 */
.balance-card-new.commission-theme {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.1) 0%,
    rgba(249, 115, 22, 0.1) 100%);
}

.balance-card-new.commission-theme .card-glow {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.3) 0%,
    rgba(249, 115, 22, 0.3) 100%);
}

.balance-card-new.commission-theme .card-icon-premium {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
  color: white;
}

.balance-card-new.commission-theme .card-indicator {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
}

/* 账户余额卡片 - 紫色主题 */
.balance-card-new.account-theme {
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 100%);
}

.balance-card-new.account-theme .card-glow {
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.3) 0%,
    rgba(147, 51, 234, 0.3) 100%);
}

.balance-card-new.account-theme .card-icon-premium {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  color: white;
}

.balance-card-new.account-theme .card-indicator {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
}

/* 我的钱包卡片 - 蓝色主题 */
.balance-card-new.wallet-theme {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(37, 99, 235, 0.1) 100%);
}

.balance-card-new.wallet-theme .card-glow {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(37, 99, 235, 0.3) 100%);
}

.balance-card-new.wallet-theme .card-icon-premium {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.balance-card-new.wallet-theme .card-indicator {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

/* 新卡片内容样式 */
.balance-card-new .card-content-premium {
  padding: 12px 8px;
  height: 100%;
}

.balance-card-new .card-header-premium {
  margin-bottom: 6px;
}

.balance-card-new .card-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  gap: 2px;
  align-items: center;
  text-align: center;
}

.balance-card-new .card-icon-premium {
  width: 28px;
  height: 28px;
  min-width: 28px;
  min-height: 28px;
}

.balance-card-new .card-icon-premium svg {
  width: 14px !important;
  height: 14px !important;
}

.balance-card-new .card-amount {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.balance-card-new .card-label-premium {
  font-size: 9px;
  line-height: 1.2;
}

/* 充值按钮样式 - 新卡片专用（默认小尺寸） */
.balance-card-new .ios-card-action {
  position: absolute;
  bottom: 6px;
  right: 6px;
  font-size: 9px;
  color: white;
  font-weight: 600;
  background: linear-gradient(135deg, #007AFF 0%, #0056CC 100%);
  border: none;
  padding: 4px 8px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow:
    0 2px 6px rgba(0, 122, 255, 0.25),
    0 1px 2px rgba(0, 0, 0, 0.08);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
}

.balance-card-new .ios-card-action:hover {
  background: linear-gradient(135deg, #0056CC 0%, #003D99 100%);
  transform: translateY(-1px) scale(1.02);
  box-shadow:
    0 4px 12px rgba(0, 122, 255, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.15);
}

.balance-card-new .ios-card-action:active {
  transform: translateY(0) scale(0.98);
  box-shadow:
    0 1px 4px rgba(0, 122, 255, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.balance-card-mini:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 16px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: rgba(255, 255, 255, 1);
}

/* 发光效果 */
.card-glow {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(45deg,
    rgba(255, 107, 107, 0.4) 0%,
    rgba(78, 205, 196, 0.4) 25%,
    rgba(69, 183, 209, 0.4) 50%,
    rgba(255, 154, 158, 0.4) 75%,
    rgba(255, 107, 107, 0.4) 100%);
  border-radius: 28px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.balance-card-mini:hover .card-glow {
  opacity: 1;
}

/* 使用原来的premium样式，只调整尺寸 */
/* balance-card-mini 复用 balance-card-new 的样式，避免重复定义 */
.balance-card-mini .card-content-premium,
.balance-card-mini .card-header-premium,
.balance-card-mini .card-main-content,
.balance-card-mini .card-icon-premium,
.balance-card-mini .card-icon-premium svg,
.balance-card-mini .card-amount,
.balance-card-mini .card-label-premium {
  /* 继承 balance-card-new 的对应样式 */
}

/* 世界一流设计师风格 - 精致卡片组 (保留原样式以防其他地方使用) */
.balance-cards-premium {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.balance-card-premium {
  position: relative;
  width: 160px;
  height: 200px;
  min-width: 160px;
  max-width: 160px;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 8px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
  cursor: pointer;
  overflow: hidden;
}

.balance-card-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%);
  border-radius: 24px;
  z-index: 1;
}

.balance-card-premium:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.15),
    0 16px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, transparent 0%, transparent 100%);
  border-radius: 26px;
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 0;
}

.balance-card-premium:hover .card-glow {
  opacity: 1;
}

/* 浮动充值按钮 */
.recharge-btn-float {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 10px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  color: #3b82f6;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.recharge-btn-float:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-2px) scale(1.05);
  box-shadow:
    0 8px 24px rgba(59, 130, 246, 0.3),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 佣金余额卡片 - 橙色主题 */
.commission-premium {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.1) 0%,
    rgba(249, 115, 22, 0.1) 100%);
}

.commission-premium .card-glow {
  background: linear-gradient(135deg,
    rgba(251, 146, 60, 0.3) 0%,
    rgba(249, 115, 22, 0.3) 100%);
}

.commission-premium .card-icon-premium {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
  color: white;
}

.commission-premium .card-indicator {
  background: linear-gradient(135deg, #fb923c 0%, #f97316 100%);
}

/* 账户余额卡片 - 紫色主题 */
.account-premium {
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.1) 0%,
    rgba(147, 51, 234, 0.1) 100%);
}

.account-premium .card-glow {
  background: linear-gradient(135deg,
    rgba(168, 85, 247, 0.3) 0%,
    rgba(147, 51, 234, 0.3) 100%);
}

.account-premium .card-icon-premium {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
  color: white;
}

.account-premium .card-indicator {
  background: linear-gradient(135deg, #a855f7 0%, #9333ea 100%);
}

/* 我的钱包卡片 - 蓝色主题 */
.wallet-premium {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(37, 99, 235, 0.1) 100%);
}

.wallet-premium .card-glow {
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.3) 0%,
    rgba(37, 99, 235, 0.3) 100%);
}

.wallet-premium .card-icon-premium {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
}

.wallet-premium .card-indicator {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.card-content-premium {
  position: relative;
  padding: 32px 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  z-index: 2;
}

.card-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  gap: 16px;
}

.card-header-premium {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.card-icon-premium {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  min-width: 48px;
  min-height: 48px;
  border-radius: 16px;
  box-shadow:
    0 8px 16px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  flex-shrink: 0;
}

.card-icon-premium svg {
  width: 24px !important;
  height: 24px !important;
  min-width: 24px;
  min-height: 24px;
  max-width: 24px;
  max-height: 24px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  flex-shrink: 0;
  display: block;
}

.card-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.card-type {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: #64748b;
  opacity: 0.8;
}

.card-indicator {
  width: 32px;
  height: 4px;
  border-radius: 2px;
  opacity: 0.8;
}

.card-amount {
  font-size: 28px;
  font-weight: 800;
  color: #1e293b;
  letter-spacing: -1px;
  line-height: 1.1;
  margin-bottom: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.card-label-premium {
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  opacity: 0.9;
}



/* 充值模态框样式 */
.modal-header {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-weight: 600;
}

.modal-header svg {
  color: #3b82f6;
}

.recharge-content {
  padding: 20px 0;
}

.current-balance {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
  border-radius: 12px;
  margin-bottom: 24px;
}

.current-balance .label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.current-balance .amount {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
}

.recharge-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-item label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.quick-amounts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.amount-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.amount-btn {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.amount-btn:hover {
  border-color: #3b82f6;
  color: #3b82f6;
}

.amount-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.recharge-info {
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.info-item .highlight {
  font-weight: 700;
  color: #059669;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 支付方式选择模态框样式 */
.payment-content {
  padding: 20px 0;
}

.payment-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
  border-radius: 12px;
  margin-bottom: 24px;
}

.payment-info .label {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.payment-info .amount {
  font-size: 18px;
  font-weight: 700;
  color: #3b82f6;
}

.payment-methods-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.no-payment-methods {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
}

.payment-method-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.payment-method-item:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.02);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.method-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  background: #f8fafc;
  margin-right: 16px;
  flex-shrink: 0;
}

.method-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.method-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.method-desc {
  font-size: 14px;
  color: #64748b;
}

.method-arrow {
  color: #9ca3af;
  transition: all 0.2s ease;
}

.payment-method-item:hover .method-arrow {
  color: #3b82f6;
  transform: translateX(2px);
}



/* 客户端平台选择 */
.clients-section {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin-top: 8px;
}

/* 移动端箭头指示器 - 桌面端默认隐藏 */
.mobile-platform-arrow {
  display: none;
}

/* 平台容器 - 桌面端保持原有布局 */
.platform-container {
  display: contents; /* 桌面端不影响布局 */
}

/* 订阅链接标题容器 */
.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: default;
}

/* 移动端订阅箭头指示器 - 桌面端默认隐藏 */
.mobile-subscription-arrow {
  display: none;
}

/* 展开动画 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
    max-height: 0;
  }
  to {
    opacity: 1;
    transform: translateY(0);
    max-height: 500px;
  }
}

.platform-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.platform-card {
  border-radius: 16px;
  padding: 20px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Windows平台 - 蓝色渐变 */
.platform-card.windows {
  background: linear-gradient(135deg, #e3f2fd 0%, #a4caf4 100%);
  color: #1565c0;
}

/* Android平台 - 绿色渐变 */
.platform-card.android {
  background: linear-gradient(135deg, #e8f5e8 0%, #a9ddc5 100%);
  color: #2e7d32;
}

/* iOS平台 - 紫蓝色渐变 */
.platform-card.ios {
  background: linear-gradient(135deg, #f3f4ff 0%, #9ea3ff 100%);
  color: #3f51b5;
}

/* 悬停效果 - 统一处理 */
.platform-card.windows:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 16px 48px rgba(0, 120, 212, 0.4);
}

.platform-card.android:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 16px 48px rgba(61, 220, 132, 0.4);
}

.platform-card.ios:hover {
  transform: translateY(-8px) scale(1.05);
  box-shadow: 0 16px 48px rgba(0, 122, 255, 0.4);
}

/* 激活状态 - 统一处理 */
.platform-card.active {
  transform: translateY(-4px) scale(1.02);
  border: 3px solid #FFD700;
  position: relative;
}

.platform-card.windows.active {
  box-shadow: 0 12px 40px rgba(0, 120, 212, 0.5);
}

.platform-card.android.active {
  box-shadow: 0 12px 40px rgba(61, 220, 132, 0.5);
}

.platform-card.ios.active {
  box-shadow: 0 12px 40px rgba(0, 122, 255, 0.5);
}

/* 激活状态的选中标识 */
.platform-card.active::after {
  content: '✓';
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 添加光泽效果 */
.platform-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.platform-card:hover::before {
  opacity: 1;
}

.platform-icon {
  margin-bottom: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.platform-icon svg {
  width: 40px;
  height: 40px;
}

.platform-card:hover .platform-icon {
  transform: scale(1.1);
}

.platform-name {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
  position: relative;
  z-index: 1;
}

.platform-desc {
  font-size: 12px;
  opacity: 0.8;
  font-weight: 400;
  position: relative;
  z-index: 1;
}

/* 客户端应用 */
.client-apps {
  border-top: 2px solid #f0f0f0;
  padding-top: 24px;
  border-radius: 16px;
  padding: 24px;
  margin-top: 8px;
  transition: all 0.3s ease;
}

.apps-title {
  font-size: 18px;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  display: inline-flex;
  align-items: center;
  color: #333;
}

/* 客户端应用网格布局 */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 20px;
  padding: 8px 0;
}

.app-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
}

.app-item:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.app-icon-wrapper {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
}

.app-item:hover .app-icon-wrapper {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.app-icon {
  width: 100%;
  height: 100%;
  max-width: 56px;
  max-height: 56px;
  object-fit: contain;
  border-radius: 8px;
  /* 禁止右键和拖拽保护 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  /* 保持点击功能，只禁用右键菜单 */
}

.app-name {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 4px;
  line-height: 1.2;
}

.app-version {
  font-size: 12px;
  color: #666;
  text-align: center;
  line-height: 1;
}

/* 订阅链接卡片区域 */
.subscription-cards-section {
  margin-top: 32px;
}

/* 简洁的订阅链接列表 */
.subscription-list-all {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 16px;
  max-width: 100%;
}



.subscription-item-simple {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(102, 201, 179, 0.15);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 0;
  flex-shrink: 0;
}

.subscription-item-simple:hover {
  background: rgba(102, 201, 179, 0.1);
  border-color: rgba(102, 201, 179, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 201, 179, 0.2);
}

.subscription-icon-simple {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.subscription-logo-simple {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.subscription-icon-simple.general svg {
  width: 20px;
  height: 20px;
}

.subscription-name-simple {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  white-space: nowrap;
  line-height: 1.2;
}



.subscription-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 18px 14px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* 订阅卡片的背景色 */
.subscription-item:nth-child(1) {
  background: linear-gradient(135deg, rgba(248, 249, 250, 0.95) 0%, rgba(233, 236, 239, 0.95) 100%);
}

.subscription-item:nth-child(2) {
  background: linear-gradient(135deg, rgba(255, 59, 48, 0.08) 0%, rgba(255, 149, 0, 0.08) 100%);
}

.subscription-item:nth-child(3) {
  background: linear-gradient(135deg, rgba(52, 199, 89, 0.08) 0%, rgba(48, 209, 88, 0.08) 100%);
}

.subscription-item:nth-child(4) {
  background: linear-gradient(135deg, rgba(88, 86, 214, 0.08) 0%, rgba(175, 82, 222, 0.08) 100%);
}

.subscription-item:nth-child(5) {
  background: linear-gradient(135deg, rgba(255, 149, 0, 0.08) 0%, rgba(255, 204, 2, 0.08) 100%);
}

.subscription-item:nth-child(6) {
  background: linear-gradient(135deg, rgba(50, 215, 75, 0.08) 0%, rgba(0, 199, 190, 0.08) 100%);
}

.subscription-item:nth-child(7) {
  background: linear-gradient(135deg, rgba(139, 69, 19, 0.08) 0%, rgba(160, 82, 45, 0.08) 100%);
}

.subscription-item:nth-child(8) {
  background: linear-gradient(135deg, rgba(255, 20, 147, 0.08) 0%, rgba(199, 21, 133, 0.08) 100%);
}

.subscription-item:nth-child(9) {
  background: linear-gradient(135deg, rgba(30, 144, 255, 0.08) 0%, rgba(0, 123, 255, 0.08) 100%);
}

.subscription-item:nth-child(10) {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.08) 0%, rgba(255, 140, 0, 0.08) 100%);
}

.subscription-item:nth-child(11) {
  background: linear-gradient(135deg, rgba(128, 0, 128, 0.08) 0%, rgba(147, 112, 219, 0.08) 100%);
}

.subscription-item:nth-child(12) {
  background: linear-gradient(135deg, rgba(220, 20, 60, 0.08) 0%, rgba(255, 69, 0, 0.08) 100%);
}

.subscription-item:nth-child(13) {
  background: linear-gradient(135deg, rgba(0, 128, 128, 0.08) 0%, rgba(72, 209, 204, 0.08) 100%);
}

.subscription-item:hover {
  transform: translateY(-3px) scale(1.01);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  border-color: rgba(255, 255, 255, 0.4);
}

.subscription-item:active {
  transform: translateY(-1px) scale(1.005);
  transition: all 0.1s ease;
}

.subscription-icon {
  width: 44px;
  height: 44px;
  margin: 0 auto 10px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  flex-shrink: 0;
}

/* 禁止订阅图标被右键保存或拖拽 */
.subscription-icon svg,
.subscription-icon img {
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  -webkit-context-menu: none;
  -moz-context-menu: none;
  context-menu: none;
}

.subscription-icon.general {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.subscription-icon.clash {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.subscription-icon.v2ray {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.subscription-icon.shadowsocks {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.subscription-icon.singbox {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.subscription-icon.hiddify {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.subscription-logo {
  width: 28px;
  height: 28px;
  object-fit: contain;
  border-radius: 6px;
  /* 禁止右键保存和新标签打开 */
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
  /* 禁用右键菜单 */
  -webkit-context-menu: none;
  -moz-context-menu: none;
  context-menu: none;
}

.subscription-name {
  font-size: 14px;
  font-weight: 600;
  color: #1d1d1f;
  margin-bottom: 3px;
  line-height: 1.3;
  text-align: center;
}

.subscription-desc {
  font-size: 11px;
  color: #8e8e93;
  line-height: 1.2;
  font-weight: 500;
  text-align: center;
  margin-top: auto;
}





.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
}

.link-input-container {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.link-input {
  flex: 1;
}

.copy-button {
  border-radius: 8px;
}

.quick-import {
  margin-bottom: 16px;
}

.import-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.import-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.client-icons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 16px;
}

.client-icon-item {
  font-size: 24px;
  opacity: 0.6;
}



/* 通知区域样式 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  flex-shrink: 0;
}

.title-icon {
  color: #007AFF;
}

.notice-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  overflow-y: auto;
  /* 确保内容不超出容器 */
  width: 100%;
  box-sizing: border-box;
}

.notices-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-right: 8px;
  /* 确保容器不超出边界 */
  width: 100%;
  box-sizing: border-box;
}

/* iOS风格滚动条 */
.notice-content::-webkit-scrollbar {
  width: 4px;
}

.notice-content::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 2px;
}

.notice-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  transition: all 0.3s ease;
}

.notice-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.4);
}

.notice-item {
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 12px;
  border-left: 4px solid #66c9b3;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(102, 201, 179, 0.2);
  margin-bottom: 12px;
  box-shadow:
    0 2px 8px rgba(102, 201, 179, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);
  /* 确保通知项不超出容器 */
  width: 100%;
  box-sizing: border-box;
}

.notice-item:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow:
    0 8px 24px rgba(102, 201, 179, 0.15),
    0 4px 12px rgba(102, 201, 179, 0.1),
    0 2px 6px rgba(0, 0, 0, 0.06);
  border-left-color: #4db6ac;
  border-color: rgba(102, 201, 179, 0.3);
}

.notice-content-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

/* 通知图片样式 */
.notice-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: #e9ecef;
}

.notice-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notice-text-content {
  flex: 1;
  min-width: 0;
}

/* 有图片时的布局调整 */
.notice-item.has-image .notice-header {
  margin-bottom: 6px;
}

.notice-item.has-image .notice-text {
  margin-bottom: 6px;
}

.notice-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.notice-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  flex: 1;
  margin-right: 12px;
}

.notice-time {
  font-size: 11px;
  color: #999;
  white-space: nowrap;
}

.notice-text {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
}

.notice-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.notice-tag {
  padding: 2px 8px;
  background: #007AFF;
  color: white;
  font-size: 10px;
  border-radius: 12px;
  font-weight: 500;
}

/* 无通知状态 */
.no-notices {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #999;
}

.no-notices-icon {
  margin-bottom: 12px;
  opacity: 0.5;
}

.no-notices-text {
  font-size: 14px;
}

/* 加载状态 */
.notice-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;
  color: #999;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}







/* 重命名为专门的流量仪表盘容器，避免与其他gauge-chart冲突 */
.traffic-gauge-container {
  display: flex;
  align-items: center;
  gap: 20px;
  background: linear-gradient(135deg,
    rgba(34, 197, 94, 0.12) 0%,
    rgba(16, 185, 129, 0.1) 50%,
    rgba(5, 150, 105, 0.12) 100%);
  border-radius: 16px;
  padding: 20px;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(255, 255, 255, 0.9) inset;
  backdrop-filter: blur(10px);
  width: 100%;
  box-sizing: border-box;
  min-height: 200px;
  overflow: visible;
  /* 智能换行：当空间不够时自动换行 */
  flex-wrap: wrap;
  justify-content: center;
}

.gauge-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border-radius: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.gauge-container:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.12),
    0 4px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.gauge-center {
  position: absolute;
  text-align: center;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 200px;
  max-width: 200px;
}

.gauge-percentage {
  font-size: 42px;
  font-weight: 900;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  letter-spacing: -1px;
}

.gauge-label {
  font-size: 16px;
  color: #374151;
  font-weight: 600;
  margin-bottom: 4px;
  letter-spacing: 0.5px;
}

.gauge-sublabel {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  margin-bottom: 8px;
}

.gauge-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  font-size: 11px;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 12px;
  margin-top: 6px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.gauge-status .status-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  flex-shrink: 0;
  animation: pulse 2s infinite;
}

.status-normal {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-normal .status-dot {
  background: #10b981;
}

.status-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-warning .status-dot {
  background: #f59e0b;
}

.status-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.status-danger .status-dot {
  background: #ef4444;
}



.progress-dot {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}



/* 智能响应式流量统计卡片容器 - 2x2网格自适应 */
.traffic-stats-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8px;
  flex: 1 1 0;
  min-width: 0;
  width: 100%;
  box-sizing: border-box;
  flex-shrink: 1;
  overflow: hidden;
}

/* 所有分辨率都保持2x2网格布局，确保视觉协调 */

.stat-item {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.6) 100%);
  border-radius: 12px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow:
    0 2px 6px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.8) inset;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.6);
  min-height: 60px;
  /* 2x2网格中的智能自适应 */
  min-width: 0;
  width: 100%;
  min-height: 60px;
}

.stat-item:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 10px rgba(0, 0, 0, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.9) inset;
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.total-traffic .stat-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.used-traffic .stat-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
}

.remaining-traffic .stat-icon {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.expire-time .stat-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.stat-content {
  flex: 1;
  text-align: left;
  min-width: 0; /* 确保可以收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
  font-weight: 500;
  letter-spacing: -0.1px;
  line-height: 1.2;
  white-space: nowrap; /* 标题不换行 */
  overflow: hidden;
  text-overflow: ellipsis;
}

.stat-value {
  font-size: 15px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: -0.2px;
  line-height: 1.3;
  word-break: break-word; /* 允许长数值换行 */
  max-width: 100%;
}

/* 专业响应式设计系统 - 基于内容最小宽度 */

/* 超大屏幕：理想布局 */
@media (min-width: 1400px) {
  .main-layout {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
}

/* 大屏幕：平衡布局 */
@media (min-width: 1200px) and (max-width: 1399px) {
  .main-layout {
    grid-template-columns: minmax(380px, 0.9fr) minmax(480px, 1.1fr);
    gap: 20px;
  }

  /* 优化右侧内容显示 */
  .right-column {
    min-width: 480px;
  }

  .notice-section,
  .traffic-dashboard {
    padding: 16px;
  }
}

/* 中等屏幕：紧凑布局 */
@media (min-width: 1000px) and (max-width: 1199px) {
  .main-layout {
    grid-template-columns: minmax(350px, 0.8fr) minmax(450px, 1.2fr);
    gap: 16px;
  }

  .right-column {
    min-width: 450px;
  }

  /* 中等屏幕下的用户卡片优化 */
  .user-card-container {
    gap: 16px;
    padding: 24px;
    height: 300px !important; /* 卡片缩小后恢复原高度 */
  }

  .balance-cards-right {
    min-width: 180px;
    max-width: 200px;
    gap: 10px;
  }

  .balance-card-mini {
    padding: 12px;
  }

  .card-amount-mini {
    font-size: 14px;
  }
}

/* 小屏幕：垂直布局 */
@media (max-width: 999px) {
  .main-layout {
    display: flex !important;
    flex-direction: column !important;
    gap: 20px !important;
    grid-template-columns: none !important;
  }

  /* left-column, right-column样式继承.main-layout.force-vertical的定义 */

  /* 垂直布局时优化右侧内容 */
  .right-column {
    order: 2;
  }

  .left-column {
    order: 1;
  }

  /* 小屏幕下切换到垂直布局 */
  .user-card-container {
    flex-direction: column !important;
    gap: 16px !important;
    padding: 20px !important;
    height: auto !important; /* 移除固定高度，让内容自然流动 */
    overflow-y: visible !important; /* 确保没有滚动条 */
  }

  .balance-cards-right {
    min-width: 0 !important;
    max-width: none !important;
    flex-direction: row !important;
    gap: 8px !important;
  }

  .balance-card-mini {
    flex: 1 !important;
    min-width: 0 !important;
    padding: 10px !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }

  .card-content-mini {
    align-items: center !important;
  }

  .card-amount-mini {
    font-size: 13px !important;
  }

  .card-label-mini {
    font-size: 10px !important;
  }

  .recharge-btn-mini {
    margin-left: 0 !important;
    margin-top: 4px !important;
  }

  .balance-cards-premium {
    gap: 16px !important;
  }

  .balance-card-premium {
    width: 140px !important;
    height: 180px !important;
  }

  /* card-content-premium padding在480px媒体查询中有更详细的定义 */

  /* card-amount font-size在其他媒体查询中有更具体的定义 */
}

@media (max-width: 768px) and (max-height: 1000px) {
  .dashboard-page {
    padding: 12px !important;
  }

  /* 768px下右侧列宽度重置 - 防止溢出 */
  .right-column {
    min-width: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* main-layout gap继承480px媒体查询中的定义 */

  /* 移动端平台卡片优化 */
  .platform-card {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    cursor: pointer !important;
  }

  /* 移动端隐藏选中标识 */
  .platform-card.active::after {
    display: none !important;
  }

  .platform-content {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
  }

  .platform-info {
    display: flex !important;
    flex-direction: column !important;
  }

  /* 移动端箭头指示器 */
  .mobile-platform-arrow {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 24px !important;
    height: 24px !important;
    transition: transform 0.3s ease !important;
    opacity: 0.6 !important;
  }

  .mobile-platform-arrow.rotated {
    transform: rotate(180deg) !important;
  }

  .mobile-platform-arrow svg {
    width: 16px !important;
    height: 16px !important;
  }

  /* 移动端平台容器 */
  .platform-container {
    display: block !important;
    margin-bottom: 16px !important;
  }

  .platform-container:last-child {
    margin-bottom: 0 !important;
  }

  .mobile-client-apps {
    margin-top: 12px !important;
    padding: 16px !important;
    background: var(--el-bg-color-page) !important;
    border-radius: 12px !important;
    border: 1px solid var(--el-border-color-light) !important;
    animation: slideDown 0.3s ease !important;
  }

  /* 隐藏桌面端客户端显示 */
  .client-apps {
    display: none !important;
  }

  /* 移动端订阅链接优化 */
  .subscription-header {
    cursor: pointer !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 12px 16px !important;
    background: rgba(136, 193, 82, 0.08) !important;
    border-radius: 10px !important;
    margin-bottom: 8px !important;
    transition: all 0.2s ease !important;
  }

  .subscription-header:active {
    background: rgba(136, 193, 82, 0.12) !important;
    transform: scale(0.98) !important;
  }

  .mobile-subscription-arrow {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 28px !important;
    height: 28px !important;
    background: rgba(136, 193, 82, 0.15) !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
    opacity: 0.9 !important;
  }

  .mobile-subscription-arrow.rotated {
    transform: rotate(180deg) !important;
    background: rgba(136, 193, 82, 0.2) !important;
  }

  .mobile-subscription-arrow svg {
    width: 16px !important;
    height: 16px !important;
    color: #88C152 !important;
  }

  /* 移动端订阅卡片展开/收缩 */
  .subscription-cards-section {
    max-height: 0 !important;
    overflow: hidden !important;
    transition: max-height 0.3s ease, opacity 0.3s ease !important;
    opacity: 0 !important;
  }

  .subscription-cards-section.mobile-expanded {
    max-height: 1000px !important;
    opacity: 1 !important;
    margin-top: 16px !important;
  }

  /* left-column, right-column样式继承480px媒体查询中的定义 */

  /* user-card padding继承基础样式定义 */

  .user-main-info {
    flex-direction: column !important;
    gap: 16px !important;
  }

  /* 移动端用户资料区域控制 */
  .user-profile {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  .user-details {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
  }

  /* 移动端订阅信息容器控制 */
  .user-card .subscription-items {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .user-card .user-subscription-item {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    padding: 4px 6px !important;
    margin-bottom: 2px !important;
  }

  .user-card .subscription-value,
  .user-card .subscription-value-container {
    max-width: 100px !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
    box-sizing: border-box !important;
  }

  .user-profile {
    align-self: center !important;
  }



  .balance-cards-premium {
    flex-direction: column !important;
    gap: 12px !important;
    align-items: center !important; /* 改为居中对齐 */
    justify-content: center !important; /* 垂直居中 */
  }

  /* 移动端优化 - 强制解除所有容器限制 */
  .user-card {
    overflow: visible !important; /* 解除用户卡片限制 */
  }

  /* 移动端用户卡片优化 - 合并重复定义 */

  .balance-card-mini {
    padding: 12px !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 12px !important;
  }

  .card-content-mini {
    flex: 1 !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
  }

  .card-amount-mini {
    font-size: 14px !important;
  }

  .recharge-btn-mini {
    margin-left: 8px !important;
    margin-top: 0 !important;
  }

  .balance-cards-premium {
    overflow: visible !important; /* 允许子元素溢出 */
    width: auto !important; /* 解除宽度限制 */
    max-width: none !important; /* 解除最大宽度 */
  }

  /* 移动端卡片样式 - 保持在容器内 */
  .balance-card-premium.commission-card-wide {
    width: 100% !important; /* 保持在容器内 */
    min-width: auto !important; /* 解除最小宽度 */
    max-width: 100% !important; /* 限制最大宽度 */
    position: relative !important;
    margin-right: 0 !important; /* 移除负边距 */
    z-index: 10 !important;
    flex-shrink: 0 !important; /* 不被压缩 */
    overflow: hidden !important; /* 防止内容溢出 */
    box-sizing: border-box !important; /* 确保正确的盒模型 */
  }

  .commission-card-wide .card-content-premium {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 16px 20px !important;
    gap: 16px !important;
    width: 100% !important; /* 确保占满容器宽度 */
  }

  /* 左侧：图标 */
  .commission-card-wide .card-header-premium {
    flex-shrink: 0 !important;
    margin: 0 !important;
  }

  .commission-card-wide .card-icon-premium {
    width: 40px !important;
    height: 40px !important;
    border-radius: 10px !important;
    background: #FF9500 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .commission-card-wide .card-icon-premium svg {
    width: 20px !important;
    height: 20px !important;
    color: white !important;
  }

  /* 隐藏不需要的元素 */
  .commission-card-wide .card-meta {
    display: none !important;
  }

  /* 右侧：文字和金额区域 */
  .commission-card-wide .card-main-content {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 4px !important;
    margin-left: auto !important;
    flex: 1 !important; /* 占据剩余空间 */
    justify-content: center !important;
    text-align: right !important;
  }

  .commission-card-wide .card-label-premium {
    font-size: 13px !important;
    color: #8E8E93 !important;
    font-weight: 400 !important;
    margin: 0 !important;
    order: 1 !important;
  }

  .commission-card-wide .card-amount {
    font-size: 17px !important;
    font-weight: 600 !important;
    color: #000000 !important;
    margin: 0 !important;
    order: 2 !important;
  }

  /* account-card-wide继承commission-card-wide的样式，只调整overflow */
  .balance-card-premium.account-card-wide {
    overflow: visible !important; /* 与commission-card-wide的差异 */
  }

  .account-card-wide .card-content-premium {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 16px 20px !important;
    gap: 16px !important;
    width: 100% !important;
  }

  /* account-card-wide .card-header-premium继承commission-card-wide的样式 */

  .account-card-wide .card-icon-premium {
    width: 40px !important;
    height: 40px !important;
    border-radius: 10px !important;
    background: #007AFF !important; /* iOS蓝色 */
    /* display, align-items, justify-content继承commission-card-wide的定义 */
  }

  .account-card-wide .card-icon-premium svg {
    width: 20px !important;
    height: 20px !important;
    color: white !important;
  }

  .account-card-wide .card-meta {
    display: none !important;
  }

  /* account-card-wide .card-main-content继承commission-card-wide的样式 */

  /* account-card-wide .card-label-premium继承commission-card-wide的样式 */

  /* account-card-wide .card-amount继承commission-card-wide的样式 */

  /* 钱包卡片拉宽样式 - 移动端保持在容器内 */
  /* wallet-card-wide继承commission-card-wide的样式，只调整overflow */
  .balance-card-premium.wallet-card-wide {
    overflow: visible !important; /* 与commission-card-wide的差异 */
  }

  .wallet-card-wide .card-content-premium {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 16px 20px !important;
    gap: 16px !important;
    width: 100% !important;
  }

  /* wallet-card-wide .card-header-premium继承commission-card-wide的样式 */

  .wallet-card-wide .card-icon-premium {
    width: 40px !important;
    height: 40px !important;
    border-radius: 10px !important;
    background: #34C759 !important; /* iOS绿色 */
    /* display, align-items, justify-content继承commission-card-wide的定义 */
  }

  .wallet-card-wide .card-icon-premium svg {
    width: 20px !important;
    height: 20px !important;
    color: white !important;
  }

  .wallet-card-wide .card-meta {
    display: none !important;
  }

  /* wallet-card-wide .card-main-content继承commission-card-wide的样式 */

  /* wallet-card-wide .card-label-premium继承commission-card-wide的样式 */

  /* wallet-card-wide .card-amount继承commission-card-wide的样式 */

  /* 钱包卡片充值按钮 */
  .wallet-card-wide .ios-card-action {
    font-size: 13px !important;
    color: #007AFF !important;
    font-weight: 400 !important;
    background: none !important;
    border: none !important;
    padding: 0 !important;
    line-height: 1 !important;
    cursor: pointer !important;
    order: 3 !important;
    margin-top: 2px !important;
  }

  .wallet-card-wide .ios-card-action:active {
    opacity: 0.6 !important;
  }

  /* 移动端隐藏桌面端浮动按钮 */
  .wallet-card-wide .recharge-btn-float {
    display: none !important;
  }

  .balance-card-premium {
    width: 100% !important;
    height: 120px !important;
    border-radius: 20px !important;
  }

  /* 移动端重置流量按钮适配 */
  .subscription-value-container {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 6px !important;
  }

  .reset-traffic-btn {
    font-size: 11px !important;
    padding: 4px 8px !important;
    min-height: 24px !important;
    width: auto !important;
    align-self: flex-start !important;
  }

  /* 佣金卡片特殊处理 - 移动端保持在容器内 */
  .balance-card-premium.commission-premium {
    width: 100% !important; /* 移动端保持在容器内 */
    max-width: 100% !important;
    margin-right: 0 !important;
    box-sizing: border-box !important;
  }

  .card-content-premium {
    padding: 20px 16px !important;
    flex-direction: row !important;
    align-items: center !important;
  }

  .card-header-premium {
    margin-bottom: 0 !important;
    margin-right: 16px !important;
  }

  .card-icon-premium {
    width: 40px !important;
    height: 40px !important;
    border-radius: 12px !important;
  }

  .card-meta {
    display: none !important;
  }

  .card-main-content {
    gap: 8px !important;
  }

  .card-amount {
    font-size: 20px !important;
    margin-bottom: 0 !important;
  }

  .card-label-premium {
    font-size: 12px !important;
  }

  .recharge-btn-float {
    top: 6px !important;
    right: 6px !important;
    padding: 3px 8px !important;
    font-size: 10px !important;
    border-radius: 10px !important;
  }

  .greeting-text {
    font-size: 12px !important;
  }

  .user-email-main {
    font-size: 20px !important;
  }

  .user-email-domain {
    font-size: 14px !important;
  }

  .user-status-badge {
    font-size: 11px !important;
    padding: 4px 8px !important;
  }

  .user-badges {
    gap: 6px !important;
  }

  .notice-section {
    padding: 16px !important;
    height: 300px !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  /* 中等屏幕通知容器控制 - 防止白色容器超出边界 */
  /* notice相关样式简化，box-sizing继承全局设置 */
  .notices-list {
    padding-right: 6px !important;
  }

  .notice-item {
    margin-right: 0 !important;
  }

  .traffic-dashboard {
    padding: 16px !important;
    height: auto !important;
    min-height: 300px !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    max-width: 100% !important;
  }

  /* dashboard-container已改用traffic-gauge-container，删除过时定义 */

  .traffic-gauge-container {
    padding: 8px !important;
    flex-shrink: 0 !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
  }

  /* traffic-stats-grid已重命名为traffic-stats-cards，删除重复定义 */

  .stat-item {
    width: 100% !important;
    min-height: 80px !important;
    padding: 16px 12px !important;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }



  .stat-icon {
    width: 32px !important;
    height: 32px !important;
  }

  .stat-label {
    font-size: 11px !important;
  }

  .stat-value {
    font-size: 13px !important;
  }

  .platform-cards {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .platform-card {
    padding: 20px 16px !important;
    min-height: 120px !important;
  }

  .app-grid {
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr)) !important;
    gap: 12px !important;
  }

  /* subscription-container padding继承基础样式 */

  .subscription-grid {
    grid-template-columns: 1fr !important;
    gap: 12px !important;
  }

  .subscription-item {
    padding: 12px !important;
  }
}

@media (max-width: 480px) {
  .dashboard-page {
    padding: 8px !important;
  }

  .main-layout {
    gap: 8px !important;
  }

  /* left-column, right-column gap继承main-layout的设置 */

  /* user-card padding继承其他媒体查询中的定义 */

  .balance-cards {
    gap: 6px !important;
  }

  /* 480px以下移动端卡片样式 - 保持在容器内 */
  .balance-card-premium.commission-card-wide {
    width: 100% !important; /* 保持在容器内 */
    min-width: auto !important;
    max-width: 100% !important;
    margin-right: 0 !important; /* 移除负边距 */
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  .balance-cards-premium {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden !important;
    box-sizing: border-box !important;
  }

  /* 480px以下卡片样式 - 合并到上面的定义中 */

  /* user-profile, user-details样式继承768px媒体查询中的定义 */

  /* subscription-items, user-subscription-item样式继承768px媒体查询，只调整padding */
  .user-card .user-subscription-item {
    padding: 3px 4px !important; /* 480px下使用更小的padding */
  }

  /* subscription-value样式继承768px媒体查询，只调整max-width */
  .user-card .subscription-value,
  .user-card .subscription-value-container {
    max-width: 80px !important; /* 480px下使用更小的max-width */
  }

  /* 手机端右侧列宽度重置 - 防止溢出 */
  .right-column {
    min-width: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
  }

  .notice-section {
    padding: 12px !important;
    height: 250px !important;
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important; /* 确保没有外边距 */
  }

  /* 移动端通知容器控制 - 防止白色容器超出边界 */
  .notice-content {
    width: 100% !important;
    box-sizing: border-box !important;
  }

  /* notice样式继承480px媒体查询，只调整padding */
  .notices-list {
    padding-right: 4px !important; /* 480px下使用更小的padding */
  }

  .notice-item {
    padding: 12px 16px !important; /* 480px下的特殊padding */
  }

  .traffic-dashboard {
    padding: 12px !important;
    height: auto !important;
    min-height: 280px !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    max-width: 100% !important;
  }



  /* traffic-gauge-container padding继承480px媒体查询中的定义 */

  .gauge-container {
    transform: scale(0.7) !important;
    transform-origin: center !important;
  }

  .gauge-percentage {
    font-size: 24px !important;
  }

  .gauge-label {
    font-size: 12px !important;
  }

  .gauge-sublabel {
    font-size: 10px !important;
  }



  .stat-item {

    width: 100% !important;
    max-width: 100% !important;
    min-height: 70px !important;
    padding: 12px 8px !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    text-align: center !important;
  }



  /* stat样式继承768px媒体查询中的定义，避免重复 */

  .platform-card {
    padding: 16px 12px !important;
    min-height: 100px !important;
  }

  .platform-icon svg {
    width: 32px !important;
    height: 32px !important;
  }

  .platform-name {
    font-size: 14px !important;
  }

  .platform-desc {
    font-size: 11px !important;
  }

  .app-grid {
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr)) !important;
    gap: 8px !important;
  }

  .app-item {
    padding: 8px !important;
  }

  .app-icon {
    width: 32px !important;
    height: 32px !important;
  }

  .app-name {
    font-size: 11px !important;
  }

  .app-version {
    font-size: 9px !important;
  }

  .subscription-container {
    padding: 12px !important;
  }

  .subscription-item {
    padding: 10px !important;
  }

  .subscription-icon svg,
  .subscription-icon img {
    width: 24px !important;
    height: 24px !important;
  }

  .subscription-name {
    font-size: 12px !important;
  }

  .subscription-desc {
    font-size: 10px !important;
  }

  /* 超小屏幕重置流量按钮适配 */
  .reset-traffic-btn {
    font-size: 10px !important;
    padding: 3px 6px !important;
    min-height: 22px !important;
  }
}

/* 移动端订阅链接优化 */
@media (max-width: 768px) {
  .subscription-list-all {
    gap: 8px !important;
  }

  .subscription-item-simple {
    padding: 6px 10px !important;
    gap: 6px !important;
    border-radius: 16px !important;
  }

  .subscription-icon-simple {
    width: 20px !important;
    height: 20px !important;
  }

  .subscription-logo-simple {
    width: 18px !important;
    height: 18px !important;
  }

  .subscription-icon-simple.general svg {
    width: 18px !important;
    height: 18px !important;
  }

  .subscription-name-simple {
    font-size: 12px !important;
  }

  .show-more-button-simple {
    font-size: 11px !important;
    padding: 5px 10px !important;
  }
}
</style>
